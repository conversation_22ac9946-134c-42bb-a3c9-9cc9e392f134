"use client";

import { useState, useEffect } from "react";
import { format } from "date-fns";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";
import { toast } from "sonner";
import { 
  Calendar as CalendarIcon, 
  Clock, 
  User, 
  FileText, 
  AlertTriangle,
  Stethoscope,
  Brain,
  CheckCircle2
} from "lucide-react";
import { EnhancedDoctor } from "@/types/doctors";
import { MedicalContext } from "@/types/medical-documents";

interface EnhancedAppointmentBookingProps {
  patientId: string;
  doctors: EnhancedDoctor[];
  medicalContext?: MedicalContext;
  preSelectedDoctorId?: string;
  onAppointmentBooked?: (appointmentId: string) => void;
}

export function EnhancedAppointmentBooking({
  patientId,
  doctors,
  medicalContext,
  preSelectedDoctorId,
  onAppointmentBooked
}: EnhancedAppointmentBookingProps) {
  const [selectedDoctor, setSelectedDoctor] = useState<string>(preSelectedDoctorId || "");
  const [date, setDate] = useState<Date>();
  const [time, setTime] = useState<string>("");
  const [notes, setNotes] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Pre-populate notes with medical context
  useEffect(() => {
    if (medicalContext && !notes) {
      const contextNotes = generateContextualNotes(medicalContext);
      setNotes(contextNotes);
    }
  }, [medicalContext, notes]);

  // Filter doctors based on medical context
  const filteredDoctors = medicalContext 
    ? doctors.filter(doctor => 
        medicalContext.urgencyLevel === "HIGH" || 
        doctor.specialty || 
        doctor.name?.toLowerCase().includes("general")
      )
    : doctors;

  // Sort doctors by relevance
  const sortedDoctors = filteredDoctors.sort((a, b) => {
    // Prioritize pre-selected doctor
    if (a.id === preSelectedDoctorId) return -1;
    if (b.id === preSelectedDoctorId) return 1;
    
    // Prioritize by urgency and availability
    if (medicalContext?.urgencyLevel === "HIGH") {
      if (a.isAvailable && !b.isAvailable) return -1;
      if (!a.isAvailable && b.isAvailable) return 1;
    }
    
    // Sort by experience
    const aExp = a.yearsOfExperience || 0;
    const bExp = b.yearsOfExperience || 0;
    return bExp - aExp;
  });

  const selectedDoctorData = doctors.find(d => d.id === selectedDoctor);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    if (!selectedDoctor || !date || !time) {
      toast.error("Please fill in all required fields.");
      setIsSubmitting(false);
      return;
    }

    const [hours, minutes] = time.split(":").map(Number);
    const scheduledAt = new Date(date);
    scheduledAt.setHours(hours);
    scheduledAt.setMinutes(minutes);

    try {
      // Enhanced appointment creation with medical context
      const appointmentData = {
        patientId,
        doctorId: selectedDoctor,
        scheduledAt,
        notes,
        medicalContext: medicalContext ? JSON.stringify(medicalContext) : null,
        urgencyLevel: medicalContext?.urgencyLevel || "LOW",
        relatedDocumentIds: medicalContext?.documentIds || [],
      };

      // TODO: Replace with actual API call
      // const result = await createEnhancedAppointment(appointmentData);
      
      toast.success("Appointment booked successfully!");
      
      if (onAppointmentBooked) {
        onAppointmentBooked("mock-appointment-id");
      }
    } catch (error) {
      toast.error("Failed to book appointment. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const generateContextualNotes = (context: MedicalContext): string => {
    const parts = [];
    
    if (context.symptoms.length > 0) {
      parts.push(`Symptoms: ${context.symptoms.slice(0, 5).join(", ")}`);
    }
    
    if (context.diagnoses.length > 0) {
      parts.push(`Conditions: ${context.diagnoses.slice(0, 3).join(", ")}`);
    }
    
    if (context.medications.length > 0) {
      parts.push(`Current medications: ${context.medications.slice(0, 3).join(", ")}`);
    }
    
    if (context.keyFindings.length > 0) {
      parts.push(`Key findings: ${context.keyFindings[0]}`);
    }
    
    parts.push(`\nBased on analysis of ${context.documentIds.length} medical document(s).`);
    
    return parts.join("\n\n");
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency?.toLowerCase()) {
      case "high":
      case "critical":
        return "destructive";
      case "medium":
        return "default";
      case "low":
      default:
        return "secondary";
    }
  };

  return (
    <div className="space-y-6">
      {/* Medical Context Summary */}
      {medicalContext && (
        <Card className="border-blue-200 dark:border-blue-800">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Brain className="w-5 h-5 text-blue-600" />
              <span>Medical Context</span>
              <Badge variant={getUrgencyColor(medicalContext.urgencyLevel)}>
                {medicalContext.urgencyLevel} Priority
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Key Symptoms:</span>
                <p className="text-gray-600 dark:text-gray-400">
                  {medicalContext.symptoms.slice(0, 3).join(", ")}
                  {medicalContext.symptoms.length > 3 && ` (+${medicalContext.symptoms.length - 3} more)`}
                </p>
              </div>
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Documents:</span>
                <p className="text-gray-600 dark:text-gray-400">
                  {medicalContext.documentIds.length} medical document(s) analyzed
                </p>
              </div>
            </div>
            
            {medicalContext.recommendedActions.length > 0 && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Recommended Actions:</span>
                <ul className="text-sm text-gray-600 dark:text-gray-400 mt-1 space-y-1">
                  {medicalContext.recommendedActions.slice(0, 2).map((action, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <CheckCircle2 className="w-3 h-3 text-green-600 mt-0.5 flex-shrink-0" />
                      <span>{action}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Appointment Booking Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CalendarIcon className="w-5 h-5 text-green-600" />
            <span>Book Appointment</span>
          </CardTitle>
          {medicalContext && (
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Doctors are pre-filtered based on your medical analysis
            </p>
          )}
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Doctor Selection */}
            <div className="space-y-2">
              <Label htmlFor="doctor">Select Doctor *</Label>
              <Select value={selectedDoctor} onValueChange={setSelectedDoctor}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a doctor" />
                </SelectTrigger>
                <SelectContent>
                  {sortedDoctors.map((doctor) => (
                    <SelectItem key={doctor.id} value={doctor.id}>
                      <div className="flex items-center justify-between w-full">
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">Dr. {doctor.name}</span>
                          {doctor.specialty && (
                            <Badge variant="outline" className="text-xs">
                              {doctor.specialty.replace('_', ' ')}
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center space-x-2 text-xs text-gray-500">
                          {doctor.yearsOfExperience && (
                            <span>{doctor.yearsOfExperience}y exp</span>
                          )}
                          {doctor.consultationFee && (
                            <span>${doctor.consultationFee}</span>
                          )}
                          {doctor.isAvailable && (
                            <Badge variant="secondary" className="text-xs">Available</Badge>
                          )}
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {selectedDoctorData && (
                <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg text-sm">
                  <div className="flex items-center space-x-2 mb-2">
                    <User className="w-4 h-4 text-gray-600" />
                    <span className="font-medium">Dr. {selectedDoctorData.name}</span>
                  </div>
                  {selectedDoctorData.bio && (
                    <p className="text-gray-600 dark:text-gray-400 text-xs">
                      {selectedDoctorData.bio}
                    </p>
                  )}
                  <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                    {selectedDoctorData.yearsOfExperience && (
                      <span>{selectedDoctorData.yearsOfExperience} years experience</span>
                    )}
                    {selectedDoctorData.consultationFee && (
                      <span>Consultation: ${selectedDoctorData.consultationFee}</span>
                    )}
                  </div>
                </div>
              )}
            </div>

            <Separator />

            {/* Date Selection */}
            <div className="space-y-2">
              <Label htmlFor="date">Select Date *</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !date && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {date ? format(date, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={date}
                    onSelect={setDate}
                    initialFocus
                    disabled={(date) => {
                      const today = new Date();
                      today.setHours(0, 0, 0, 0);

                      const maxDate = new Date();
                      maxDate.setDate(today.getDate() + 30);

                      return (
                        date < today ||
                        date > maxDate ||
                        date.getDay() === 0 // Disable Sundays
                      );
                    }}
                  />
                </PopoverContent>
              </Popover>
            </div>

            {/* Time Selection */}
            <div className="space-y-2">
              <Label htmlFor="time">Select Time *</Label>
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4 text-gray-500" />
                <Input
                  id="time"
                  type="time"
                  value={time}
                  onChange={(e) => setTime(e.target.value)}
                  required
                  min="09:00"
                  max="17:00"
                />
              </div>
              <p className="text-xs text-gray-500">
                Available hours: 9:00 AM - 5:00 PM
              </p>
            </div>

            <Separator />

            {/* Notes */}
            <div className="space-y-2">
              <Label htmlFor="notes">Additional Notes</Label>
              <Textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Any additional information or specific concerns..."
                rows={4}
              />
              {medicalContext && (
                <p className="text-xs text-blue-600 dark:text-blue-400">
                  Notes have been pre-populated based on your medical analysis
                </p>
              )}
            </div>

            {/* Urgency Notice */}
            {medicalContext?.urgencyLevel === "HIGH" && (
              <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="w-4 h-4 text-red-600" />
                  <span className="text-sm font-medium text-red-800 dark:text-red-200">
                    High Priority Appointment
                  </span>
                </div>
                <p className="text-xs text-red-700 dark:text-red-300 mt-1">
                  Based on your medical analysis, this appointment has been marked as high priority. 
                  Please consider booking the earliest available slot.
                </p>
              </div>
            )}

            {/* Submit Button */}
            <Button 
              type="submit" 
              className="w-full" 
              disabled={isSubmitting}
              size="lg"
            >
              {isSubmitting ? (
                <>
                  <Clock className="w-4 h-4 mr-2 animate-spin" />
                  Booking Appointment...
                </>
              ) : (
                <>
                  <CalendarIcon className="w-4 h-4 mr-2" />
                  Book Appointment
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
