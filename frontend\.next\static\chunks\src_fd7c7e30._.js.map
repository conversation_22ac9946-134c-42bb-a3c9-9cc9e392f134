{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/actions/users.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport prisma from \"@/lib/prisma\";\r\nimport { Role } from \"@prisma/client\";\r\n\r\n// Function to update a user's role\r\nexport async function updateUserRole(userId: string, newRole: Role) {\r\n  try {\r\n    const updatedUser = await prisma.user.update({\r\n      where: { id: userId },\r\n      data: { role: newRole },\r\n    });\r\n    return updatedUser;\r\n  } catch (error) {\r\n    console.error(\"Error updating user role:\", error);\r\n    throw new Error(\"Failed to update user role\");\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;;IAMsB,iBAAA,WAAA,GAAA,CAAA,GAAA,yNAAA,CAAA,wBAAA,EAAA,8CAAA,yNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,yNAAA,CAAA,mBAAA,EAAA", "debugId": null}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/auth/role-selection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { User, Stethoscope } from \"lucide-react\";\r\nimport { useState } from \"react\";\r\nimport { Role } from \"@prisma/client\";\r\nimport { updateUserRole } from \"@/actions/users\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { toast } from \"sonner\";\r\n\r\nexport default function RoleSelection({ userId }: { userId: string }) {\r\n  const [selectedRole, setSelectedRole] = useState<Role | null>(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const router = useRouter();\r\n\r\n  const handleRoleChange = async (role: Role) => {\r\n    setSelectedRole(role);\r\n    setIsLoading(true);\r\n    await updateUserRole(userId, role);\r\n    toast.success(`Role successfully set to ${role}`);\r\n    router.push(\"/dashboard\");\r\n  };\r\n\r\n  return (\r\n    <div className=\"max-w-md w-full space-y-6 text-center\">\r\n      <h1 className=\"text-3xl font-bold text-gray-800\">Choose Your Role</h1>\r\n      <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\r\n        <Card\r\n          onClick={() => setSelectedRole(\"PATIENT\" as Role)}\r\n          className={`w-full cursor-pointer transition-all ${\r\n            selectedRole === \"PATIENT\" ? \"ring-2 ring-sky-500\" : \"\"\r\n          }`}\r\n        >\r\n          <CardContent className=\"p-6 flex flex-col items-center\">\r\n            <User className=\"w-12 h-12 mb-2 text-sky-600\" />\r\n            <span className=\"text-lg font-medium\">Patient</span>\r\n          </CardContent>\r\n        </Card>\r\n        <Card\r\n          onClick={() => setSelectedRole(\"DOCTOR\" as Role)}\r\n          className={`w-full cursor-pointer transition-all ${\r\n            selectedRole === \"DOCTOR\" ? \"ring-2 ring-emerald-500\" : \"\"\r\n          }`}\r\n        >\r\n          <CardContent className=\"p-6 flex flex-col items-center\">\r\n            <Stethoscope className=\"w-12 h-12 mb-2 text-emerald-600\" />\r\n            <span className=\"text-lg font-medium\">Doctor</span>\r\n          </CardContent>\r\n        </Card>\r\n      </div>\r\n\r\n      <Button\r\n        disabled={!selectedRole}\r\n        className=\"w-full mt-4\"\r\n        onClick={() => handleRoleChange(selectedRole!)}\r\n      >\r\n        {isLoading\r\n          ? \"Saving...\"\r\n          : \"Continue as \" +\r\n            (selectedRole === \"PATIENT\" ? \"Patient\" : \"Doctor\")}\r\n      </Button>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;;;AATA;;;;;;;;AAWe,SAAS,cAAc,EAAE,MAAM,EAAsB;;IAClE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,mBAAmB,OAAO;QAC9B,gBAAgB;QAChB,aAAa;QACb,MAAM,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ;QAC7B,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,yBAAyB,EAAE,MAAM;QAChD,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAmC;;;;;;0BACjD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mIAAA,CAAA,OAAI;wBACH,SAAS,IAAM,gBAAgB;wBAC/B,WAAW,CAAC,qCAAqC,EAC/C,iBAAiB,YAAY,wBAAwB,IACrD;kCAEF,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAK,WAAU;8CAAsB;;;;;;;;;;;;;;;;;kCAG1C,6LAAC,mIAAA,CAAA,OAAI;wBACH,SAAS,IAAM,gBAAgB;wBAC/B,WAAW,CAAC,qCAAqC,EAC/C,iBAAiB,WAAW,4BAA4B,IACxD;kCAEF,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6LAAC,mNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;8CACvB,6LAAC;oCAAK,WAAU;8CAAsB;;;;;;;;;;;;;;;;;;;;;;;0BAK5C,6LAAC,qIAAA,CAAA,SAAM;gBACL,UAAU,CAAC;gBACX,WAAU;gBACV,SAAS,IAAM,iBAAiB;0BAE/B,YACG,cACA,iBACA,CAAC,iBAAiB,YAAY,YAAY,QAAQ;;;;;;;;;;;;AAI9D;GArDwB;;QAGP,qIAAA,CAAA,YAAS;;;KAHF", "debugId": null}}]}