// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

enum Role {
  PATIENT
  DOCTOR
  ADMIN
}

enum AppointmentStatus {
  PENDING
  CONFIRMED
  CANCELLED
  COMPLETED
}

enum DocumentType {
  PRESCRIPTION
  LAB_REPORT
  MRI_REPORT
  XRAY_REPORT
  DISCHARGE_SUMMARY
  MEDICAL_CERTIFICATE
  OTHER
}

enum AnalysisStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}

enum UrgencyLevel {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum DoctorSpecialty {
  GENERAL_MEDICINE
  CARDIOLOGY
  NEUROLOGY
  ORTHOPEDICS
  DERMATOLOGY
  PEDIATRICS
  GYNECOLOGY
  PSYCHIATRY
  RADIOLOGY
  PATHOLOGY
  ONCOLOGY
  ENDOCRINOLOGY
  GASTROENTEROLOGY
  PULMONOLOGY
  NEPHROLOGY
  OPHTHALMOLOGY
  ENT
  ANESTHESIOLOGY
  EMERGENCY_MEDICINE
  FAMILY_MEDICINE
}

model User {
  id        String   @id @map("_id")
  email     String   @unique
  name      String?
  role      Role     @default(PATIENT)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  emailVerified Boolean
  image         String?
  sessions      Session[]
  accounts      Account[]

  // Doctor-specific fields
  specialty         DoctorSpecialty?
  licenseNumber     String?
  yearsOfExperience Int?
  bio               String?
  consultationFee   Float?

  // Appointment relations
  doctorAppointments  Appointment[] @relation("DoctorAppointments")
  patientAppointments Appointment[] @relation("PatientAppointments")

  // Medical document relations
  uploadedDocuments MedicalDocument[] @relation("DocumentUploader")

  @@map("users")
}

model Appointment {
  id          String            @id @default(cuid()) @map("_id")
  patientId   String
  doctorId    String
  scheduledAt DateTime
  status      AppointmentStatus @default(PENDING)
  notes       String?

  // Medical context from document analysis
  medicalContext     String? // JSON string containing analysis results
  urgencyLevel       UrgencyLevel @default(LOW)
  relatedDocumentIds String[] // Array of related document IDs

  patient User @relation("PatientAppointments", fields: [patientId], references: [id], onDelete: Cascade)
  doctor  User @relation("DoctorAppointments", fields: [doctorId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("appointments")
}

model Session {
  id        String   @id @map("_id")
  expiresAt DateTime
  token     String
  createdAt DateTime
  updatedAt DateTime
  ipAddress String?
  userAgent String?
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([token])
  @@map("session")
}

model Account {
  id                    String    @id @map("_id")
  accountId             String
  providerId            String
  userId                String
  user                  User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  accessToken           String?
  refreshToken          String?
  idToken               String?
  accessTokenExpiresAt  DateTime?
  refreshTokenExpiresAt DateTime?
  scope                 String?
  password              String?
  createdAt             DateTime
  updatedAt             DateTime

  @@map("account")
}

model Verification {
  id         String    @id @map("_id")
  identifier String
  value      String
  expiresAt  DateTime
  createdAt  DateTime?
  updatedAt  DateTime?

  @@map("verification")
}

model MedicalDocument {
  id           String       @id @default(cuid()) @map("_id")
  fileName     String
  originalName String
  fileSize     Int
  mimeType     String
  documentType DocumentType
  filePath     String // Path to stored file
  uploadedById String

  // File metadata
  checksum    String? // For duplicate detection
  isProcessed Boolean @default(false)

  // Relations
  uploadedBy User            @relation("DocumentUploader", fields: [uploadedById], references: [id], onDelete: Cascade)
  analysis   AnalysisResult?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("medical_documents")
}

model AnalysisResult {
  id         String         @id @default(cuid()) @map("_id")
  documentId String         @unique
  status     AnalysisStatus @default(PENDING)

  // Analysis results (JSON strings for flexibility)
  extractedData          String? // Raw extracted data from Gemini
  patientInfo            String? // Patient information JSON
  symptoms               String[] // Array of symptoms
  medications            String[] // Array of medications
  diagnoses              String[] // Array of diagnoses/conditions
  suggestedTests         String[] // Array of suggested tests
  recommendedSpecialists String[] // Array of recommended specialties

  // Analysis metadata
  urgencyLevel   UrgencyLevel @default(LOW)
  confidence     Float? // Confidence score from AI
  processingTime Int? // Processing time in milliseconds
  errorMessage   String? // Error message if analysis failed

  // Smart routing results
  suggestedDoctorIds String[] // Array of suggested doctor IDs
  routingReason      String? // Explanation for routing decision

  // Relations
  document MedicalDocument @relation(fields: [documentId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("analysis_results")
}
