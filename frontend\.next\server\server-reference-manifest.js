self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"00b4ef6413cdcfd77c9f1aa606fece522653e36c4c\": {\n      \"workers\": {\n        \"app/(auth)/onboarding/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(auth)/onboarding/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/lib/session.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/users.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(auth)/onboarding/page\": \"rsc\"\n      }\n    },\n    \"6057deb139db5e5ec029c14ad25f1cf6356a934ead\": {\n      \"workers\": {\n        \"app/(auth)/onboarding/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/(auth)/onboarding/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/lib/session.ts [app-rsc] (ecmascript)\\\", ACTIONS_MODULE1 => \\\"[project]/src/actions/users.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/(auth)/onboarding/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"aUqAtsCCdWmJ30GchUL4G95uoOqTsWTZjYd6YQ/FMRs=\"\n}"