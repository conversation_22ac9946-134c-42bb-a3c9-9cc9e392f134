import { DocumentType } from "@prisma/client";
import { DocumentTypeConfig, DocumentSystemConfig } from "@/types/medical-documents";

// Document type configurations
export const DOCUMENT_TYPE_CONFIGS: DocumentTypeConfig[] = [
  {
    type: DocumentType.PRESCRIPTION,
    label: "Prescription",
    description: "Medical prescriptions and medication orders",
    acceptedMimeTypes: ["image/jpeg", "image/png", "image/webp", "application/pdf"],
    maxSize: 10 * 1024 * 1024, // 10MB
    icon: "pill",
    analysisPrompt: `
      Analyze this prescription document and extract:
      - Patient information (name, age, gender)
      - Prescribed medications with dosages
      - Doctor information
      - Date of prescription
      - Any special instructions
      - Identify potential drug interactions or concerns
    `
  },
  {
    type: DocumentType.LAB_REPORT,
    label: "Lab Report",
    description: "Laboratory test results and pathology reports",
    acceptedMimeTypes: ["image/jpeg", "image/png", "image/webp", "application/pdf"],
    maxSize: 15 * 1024 * 1024, // 15MB
    icon: "test-tube",
    analysisPrompt: `
      Analyze this lab report and extract:
      - Patient information
      - Test types and results
      - Reference ranges and abnormal values
      - Test dates
      - Recommendations or follow-up needed
      - Urgency level based on critical values
    `
  },
  {
    type: DocumentType.MRI_REPORT,
    label: "MRI Report",
    description: "MRI scan results and radiological reports",
    acceptedMimeTypes: ["image/jpeg", "image/png", "image/webp", "application/pdf"],
    maxSize: 20 * 1024 * 1024, // 20MB
    icon: "brain",
    analysisPrompt: `
      Analyze this MRI report and extract:
      - Patient information
      - Body part/region scanned
      - Key findings and abnormalities
      - Radiologist recommendations
      - Urgency level
      - Suggested follow-up or specialist referral
    `
  },
  {
    type: DocumentType.XRAY_REPORT,
    label: "X-Ray Report",
    description: "X-ray images and radiological interpretations",
    acceptedMimeTypes: ["image/jpeg", "image/png", "image/webp", "application/pdf"],
    maxSize: 15 * 1024 * 1024, // 15MB
    icon: "x-ray",
    analysisPrompt: `
      Analyze this X-ray report and extract:
      - Patient information
      - Body part examined
      - Findings and abnormalities
      - Fractures, infections, or other conditions
      - Recommendations for treatment
      - Need for urgent care
    `
  },
  {
    type: DocumentType.DISCHARGE_SUMMARY,
    label: "Discharge Summary",
    description: "Hospital discharge summaries and care instructions",
    acceptedMimeTypes: ["image/jpeg", "image/png", "image/webp", "application/pdf"],
    maxSize: 10 * 1024 * 1024, // 10MB
    icon: "file-text",
    analysisPrompt: `
      Analyze this discharge summary and extract:
      - Patient information
      - Admission and discharge dates
      - Primary and secondary diagnoses
      - Procedures performed
      - Medications prescribed
      - Follow-up instructions
      - Warning signs to watch for
    `
  },
  {
    type: DocumentType.MEDICAL_CERTIFICATE,
    label: "Medical Certificate",
    description: "Medical certificates and fitness reports",
    acceptedMimeTypes: ["image/jpeg", "image/png", "image/webp", "application/pdf"],
    maxSize: 5 * 1024 * 1024, // 5MB
    icon: "certificate",
    analysisPrompt: `
      Analyze this medical certificate and extract:
      - Patient information
      - Medical condition or fitness status
      - Validity period
      - Restrictions or recommendations
      - Doctor information and signature
    `
  },
  {
    type: DocumentType.OTHER,
    label: "Other Medical Document",
    description: "Other medical documents and reports",
    acceptedMimeTypes: ["image/jpeg", "image/png", "image/webp", "application/pdf"],
    maxSize: 10 * 1024 * 1024, // 10MB
    icon: "file",
    analysisPrompt: `
      Analyze this medical document and extract:
      - Document type and purpose
      - Patient information
      - Key medical information
      - Dates and healthcare provider details
      - Any recommendations or follow-up needed
    `
  }
];

// System configuration
export const DOCUMENT_SYSTEM_CONFIG: DocumentSystemConfig = {
  maxFileSize: 20 * 1024 * 1024, // 20MB max
  maxFilesPerUpload: 10,
  supportedTypes: DOCUMENT_TYPE_CONFIGS,
  storageProvider: "local", // Can be configured via env
  analysisProvider: "gemini",
  autoAnalysis: true,
  retentionPeriod: 365, // 1 year
};

// Accepted file types for dropzone
export const ACCEPTED_FILE_TYPES = {
  "image/jpeg": [".jpg", ".jpeg"],
  "image/png": [".png"],
  "image/webp": [".webp"],
  "application/pdf": [".pdf"],
};

// File type validation
export function validateFileType(file: File): boolean {
  return Object.keys(ACCEPTED_FILE_TYPES).includes(file.type);
}

// File size validation
export function validateFileSize(file: File, maxSize?: number): boolean {
  const limit = maxSize || DOCUMENT_SYSTEM_CONFIG.maxFileSize;
  return file.size <= limit;
}

// Get document type config
export function getDocumentTypeConfig(type: DocumentType): DocumentTypeConfig | undefined {
  return DOCUMENT_TYPE_CONFIGS.find(config => config.type === type);
}

// Format file size for display
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

// Get file extension
export function getFileExtension(filename: string): string {
  return filename.slice((filename.lastIndexOf(".") - 1 >>> 0) + 2);
}

// Generate unique filename
export function generateUniqueFilename(originalName: string): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 15);
  const extension = getFileExtension(originalName);
  return `${timestamp}_${random}.${extension}`;
}

// Check if file is an image
export function isImageFile(mimeType: string): boolean {
  return mimeType.startsWith("image/");
}

// Check if file is a PDF
export function isPdfFile(mimeType: string): boolean {
  return mimeType === "application/pdf";
}

// Get appropriate icon for file type
export function getFileTypeIcon(mimeType: string): string {
  if (isImageFile(mimeType)) return "image";
  if (isPdfFile(mimeType)) return "file-text";
  return "file";
}

// Document type display labels
export const DOCUMENT_TYPE_LABELS: Record<DocumentType, string> = {
  [DocumentType.PRESCRIPTION]: "Prescription",
  [DocumentType.LAB_REPORT]: "Lab Report",
  [DocumentType.MRI_REPORT]: "MRI Report",
  [DocumentType.XRAY_REPORT]: "X-Ray Report",
  [DocumentType.DISCHARGE_SUMMARY]: "Discharge Summary",
  [DocumentType.MEDICAL_CERTIFICATE]: "Medical Certificate",
  [DocumentType.OTHER]: "Other Document",
};

// Priority levels for document processing
export const DOCUMENT_PRIORITY_LEVELS = {
  CRITICAL: { value: 1, label: "Critical", color: "red" },
  HIGH: { value: 2, label: "High", color: "orange" },
  NORMAL: { value: 3, label: "Normal", color: "blue" },
  LOW: { value: 4, label: "Low", color: "gray" },
};

// Analysis stage descriptions
export const ANALYSIS_STAGES = [
  { threshold: 10, label: "Document Reading", description: "Reading and parsing document content" },
  { threshold: 25, label: "Content Processing", description: "Processing text and image content" },
  { threshold: 45, label: "Medical Term Analysis", description: "Analyzing medical terminology and context" },
  { threshold: 65, label: "Clinical Data Review", description: "Reviewing clinical data and findings" },
  { threshold: 85, label: "Generating Recommendations", description: "Generating recommendations and routing suggestions" },
  { threshold: 100, label: "Finalizing Results", description: "Finalizing analysis results" },
];
