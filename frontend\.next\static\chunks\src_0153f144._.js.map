{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Progress({\r\n  className,\r\n  value,\r\n  ...props\r\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\r\n  return (\r\n    <ProgressPrimitive.Root\r\n      data-slot=\"progress\"\r\n      className={cn(\r\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ProgressPrimitive.Indicator\r\n        data-slot=\"progress-indicator\"\r\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\r\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\r\n      />\r\n    </ProgressPrimitive.Root>\r\n  )\r\n}\r\n\r\nexport { Progress }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE;KArBS", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/arthamed/file-upload.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { useCallback, useState } from \"react\"\r\nimport { useDropzone } from \"react-dropzone\"\r\nimport { Upload, FileText, ImageIcon, AlertCircle } from \"lucide-react\"\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Button } from \"@/components/ui/button\"\r\nimport { Progress } from \"@/components/ui/progress\"\r\n\r\ninterface FileUploadProps {\r\n  onFileUpload: (files: File[]) => void\r\n  isUploading: boolean\r\n  acceptedTypes: Record<string, string[]>\r\n  maxSize?: number\r\n  maxFiles?: number\r\n}\r\n\r\nexport function FileUpload({\r\n  onFileUpload,\r\n  isUploading,\r\n  acceptedTypes,\r\n  maxSize = 10 * 1024 * 1024, // 10MB\r\n  maxFiles = 5,\r\n}: FileUploadProps) {\r\n  const [uploadProgress, setUploadProgress] = useState(0)\r\n\r\n  const onDrop = useCallback(\r\n    (acceptedFiles: File[], rejectedFiles: unknown[]) => {\r\n      if (rejectedFiles.length > 0) {\r\n        console.log(\"Rejected files:\", rejectedFiles)\r\n      }\r\n\r\n      if (acceptedFiles.length > 0) {\r\n        setUploadProgress(0)\r\n        const interval = setInterval(() => {\r\n          setUploadProgress((prev) => {\r\n            if (prev >= 100) {\r\n              clearInterval(interval)\r\n              return 100\r\n            }\r\n            return prev + 10\r\n          })\r\n        }, 100)\r\n\r\n        onFileUpload(acceptedFiles)\r\n      }\r\n    },\r\n    [onFileUpload],\r\n  )\r\n\r\n  const { getRootProps, getInputProps, isDragActive, isDragReject } = useDropzone({\r\n    onDrop,\r\n    accept: acceptedTypes,\r\n    maxSize,\r\n    maxFiles,\r\n    disabled: isUploading,\r\n  })\r\n\r\n  const getIcon = () => {\r\n    if (isDragActive && !isDragReject) {\r\n      return <Upload className=\"w-12 h-12 text-blue-600 dark:text-blue-400 animate-bounce\" />\r\n    }\r\n    if (isDragReject) {\r\n      return <AlertCircle className=\"w-12 h-12 text-red-500 dark:text-red-400\" />\r\n    }\r\n    return <Upload className=\"w-12 h-12 text-gray-400 dark:text-gray-500\" />\r\n  }\r\n\r\n  const getMessage = () => {\r\n    if (isDragReject) {\r\n      return \"File type not supported\"\r\n    }\r\n    if (isDragActive) {\r\n      return \"Drop your files here...\"\r\n    }\r\n    return \"Drag & drop your medical documents here\"\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div\r\n        {...getRootProps()}\r\n        className={cn(\r\n          \"border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200\",\r\n          isDragActive && !isDragReject && \"border-blue-500 bg-blue-50 dark:bg-blue-900/30\",\r\n          isDragReject && \"border-red-500 bg-red-50 dark:bg-red-900/20\",\r\n          !isDragActive && \"border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 hover:bg-gray-50 dark:hover:bg-gray-700/50\",\r\n          isUploading && \"pointer-events-none opacity-70\",\r\n        )}\r\n      >\r\n        <input {...getInputProps()} />\r\n\r\n        <div className=\"flex flex-col items-center space-y-4\">\r\n          {getIcon()}\r\n\r\n          <div>\r\n            <p className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\r\n              {getMessage()}\r\n            </p>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\r\n              or click to browse your files\r\n            </p>\r\n\r\n            <Button \r\n              variant=\"outline\" \r\n              disabled={isUploading}\r\n              className=\"dark:border-gray-600 dark:text-white dark:hover:bg-gray-700\"\r\n            >\r\n              Choose Files\r\n            </Button>\r\n          </div>\r\n\r\n          <div className=\"flex flex-wrap justify-center gap-2 text-xs text-gray-500 dark:text-gray-400\">\r\n            <span className=\"flex items-center space-x-1\">\r\n              <FileText className=\"w-3 h-3\" />\r\n              <span>PDF</span>\r\n            </span>\r\n            <span className=\"flex items-center space-x-1\">\r\n              <ImageIcon className=\"w-3 h-3\" />\r\n              <span>JPG</span>\r\n            </span>\r\n            <span className=\"flex items-center space-x-1\">\r\n              <ImageIcon className=\"w-3 h-3\" />\r\n              <span>PNG</span>\r\n            </span>\r\n            <span>• Max {maxSize / (1024 * 1024)}MB per file</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {isUploading && (\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex justify-between text-sm text-gray-700 dark:text-gray-300\">\r\n            <span>Uploading files...</span>\r\n            <span>{uploadProgress}%</span>\r\n          </div>\r\n          <Progress \r\n            value={uploadProgress} \r\n            className=\"h-2 bg-gray-200 dark:bg-gray-700\" \r\n          />\r\n        </div>\r\n      )}\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;;AAiBO,SAAS,WAAW,EACzB,YAAY,EACZ,WAAW,EACX,aAAa,EACb,UAAU,KAAK,OAAO,IAAI,EAC1B,WAAW,CAAC,EACI;;IAChB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0CACvB,CAAC,eAAuB;YACtB,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,QAAQ,GAAG,CAAC,mBAAmB;YACjC;YAEA,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,kBAAkB;gBAClB,MAAM,WAAW;+DAAY;wBAC3B;uEAAkB,CAAC;gCACjB,IAAI,QAAQ,KAAK;oCACf,cAAc;oCACd,OAAO;gCACT;gCACA,OAAO,OAAO;4BAChB;;oBACF;8DAAG;gBAEH,aAAa;YACf;QACF;yCACA;QAAC;KAAa;IAGhB,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAC9E;QACA,QAAQ;QACR;QACA;QACA,UAAU;IACZ;IAEA,MAAM,UAAU;QACd,IAAI,gBAAgB,CAAC,cAAc;YACjC,qBAAO,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAC3B;QACA,IAAI,cAAc;YAChB,qBAAO,6LAAC,uNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAChC;QACA,qBAAO,6LAAC,yMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;IAC3B;IAEA,MAAM,aAAa;QACjB,IAAI,cAAc;YAChB,OAAO;QACT;QACA,IAAI,cAAc;YAChB,OAAO;QACT;QACA,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gGACA,gBAAgB,CAAC,gBAAgB,kDACjC,gBAAgB,+CAChB,CAAC,gBAAgB,oIACjB,eAAe;;kCAGjB,6LAAC;wBAAO,GAAG,eAAe;;;;;;kCAE1B,6LAAC;wBAAI,WAAU;;4BACZ;0CAED,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDACV;;;;;;kDAEH,6LAAC;wCAAE,WAAU;kDAAgD;;;;;;kDAI7D,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,UAAU;wCACV,WAAU;kDACX;;;;;;;;;;;;0CAKH,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;;0DACd,6LAAC,iNAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAK,WAAU;;0DACd,6LAAC,2MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAK,WAAU;;0DACd,6LAAC,2MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;;4CAAK;4CAAO,UAAU,CAAC,OAAO,IAAI;4CAAE;;;;;;;;;;;;;;;;;;;;;;;;;YAK1C,6BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;0CAAK;;;;;;0CACN,6LAAC;;oCAAM;oCAAe;;;;;;;;;;;;;kCAExB,6LAAC,uIAAA,CAAA,WAAQ;wBACP,OAAO;wBACP,WAAU;;;;;;;;;;;;;;;;;;AAMtB;GA/HgB;;QAiCsD,2KAAA,CAAA,cAAW;;;KAjCjE", "debugId": null}}, {"offset": {"line": 482, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 534, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/arthamed/uploaded-files.tsx"], "sourcesContent": ["/* eslint-disable @next/next/no-img-element */\r\n\"use client\"\r\n\r\nimport { FileText, ImageIcon, X, Eye } from \"lucide-react\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\nimport { Card, CardContent } from \"@/components/ui/card\"\r\nimport { Badge } from \"@/components/ui/badge\"\r\n\r\ninterface UploadedFile {\r\n  id: string\r\n  name: string\r\n  size: number\r\n  type: string\r\n  preview?: string\r\n}\r\n\r\ninterface UploadedFilesProps {\r\n  files: UploadedFile[]\r\n  onRemoveFile: (fileId: string) => void\r\n}\r\n\r\nexport function UploadedFiles({ files, onRemoveFile }: UploadedFilesProps) {\r\n  const formatFileSize = (bytes: number) => {\r\n    if (bytes === 0) return \"0 Bytes\"\r\n    const k = 1024\r\n    const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\"]\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k))\r\n    return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i]\r\n  }\r\n\r\n  const getFileIcon = (type: string) => {\r\n    if (type.startsWith(\"image/\")) {\r\n      return <ImageIcon className=\"w-5 h-5 text-blue-600 dark:text-blue-400\" />\r\n    }\r\n    return <FileText className=\"w-5 h-5 text-red-600 dark:text-red-400\" />\r\n  }\r\n\r\n  const getFileTypeLabel = (type: string) => {\r\n    if (type === \"application/pdf\") return \"PDF\"\r\n    if (type.startsWith(\"image/\")) return type.split(\"/\")[1].toUpperCase()\r\n    return \"Unknown\"\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-3\">\r\n      <h3 className=\"font-medium text-gray-900 dark:text-white\">\r\n        Uploaded Files ({files.length})\r\n      </h3>\r\n\r\n      <div className=\"space-y-2\">\r\n        {files.map((file) => (\r\n          <Card key={file.id} className=\"border border-gray-200 dark:border-gray-700\">\r\n            <CardContent className=\"p-4\">\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center space-x-3 flex-1 min-w-0\">\r\n                  {file.preview ? (\r\n                    <div className=\"relative w-12 h-12 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800 flex-shrink-0\">\r\n                      <img\r\n                        src={file.preview || \"/placeholder.svg\"}\r\n                        alt={file.name}\r\n                        className=\"w-full h-full object-cover\"\r\n                      />\r\n                    </div>\r\n                  ) : (\r\n                    <div className=\"w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-800 flex items-center justify-center flex-shrink-0\">\r\n                      {getFileIcon(file.type)}\r\n                    </div>\r\n                  )}\r\n\r\n                  <div className=\"flex-1 min-w-0\">\r\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\r\n                      {file.name}\r\n                    </p>\r\n                    <div className=\"flex items-center space-x-2 mt-1\">\r\n                      <Badge \r\n                        variant=\"secondary\" \r\n                        className=\"text-xs dark:bg-gray-700 dark:text-gray-200\"\r\n                      >\r\n                        {getFileTypeLabel(file.type)}\r\n                      </Badge>\r\n                      <span className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n                        {formatFileSize(file.size)}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"flex items-center space-x-2 flex-shrink-0\">\r\n                  {file.preview && (\r\n                    <Button \r\n                      variant=\"ghost\" \r\n                      size=\"sm\" \r\n                      onClick={() => window.open(file.preview, \"_blank\")}\r\n                      className=\"dark:hover:bg-gray-700\"\r\n                    >\r\n                      <Eye className=\"w-4 h-4 text-gray-700 dark:text-gray-300\" />\r\n                    </Button>\r\n                  )}\r\n                  <Button\r\n                    variant=\"ghost\"\r\n                    size=\"sm\"\r\n                    onClick={() => onRemoveFile(file.id)}\r\n                    className=\"text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/30\"\r\n                  >\r\n                    <X className=\"w-4 h-4\" />\r\n                  </Button>\r\n                </div>\r\n              </div>\r\n            </CardContent>\r\n          </Card>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": "AAAA,4CAA4C;;;;AAG5C;AAAA;AAAA;AAAA;AACA;AACA;AACA;AALA;;;;;;AAoBO,SAAS,cAAc,EAAE,KAAK,EAAE,YAAY,EAAsB;IACvE,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,OAAO,UAAU,CAAC,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IAChF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,KAAK,UAAU,CAAC,WAAW;YAC7B,qBAAO,6LAAC,2MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAC9B;QACA,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC7B;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,SAAS,mBAAmB,OAAO;QACvC,IAAI,KAAK,UAAU,CAAC,WAAW,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW;QACpE,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;;oBAA4C;oBACvC,MAAM,MAAM;oBAAC;;;;;;;0BAGhC,6LAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC,mIAAA,CAAA,OAAI;wBAAe,WAAU;kCAC5B,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,OAAO,iBACX,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,KAAK,KAAK,OAAO,IAAI;oDACrB,KAAK,KAAK,IAAI;oDACd,WAAU;;;;;;;;;;qEAId,6LAAC;gDAAI,WAAU;0DACZ,YAAY,KAAK,IAAI;;;;;;0DAI1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEACV,KAAK,IAAI;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oIAAA,CAAA,QAAK;gEACJ,SAAQ;gEACR,WAAU;0EAET,iBAAiB,KAAK,IAAI;;;;;;0EAE7B,6LAAC;gEAAK,WAAU;0EACb,eAAe,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;kDAMjC,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,OAAO,kBACX,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,OAAO,EAAE;gDACzC,WAAU;0DAEV,cAAA,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;0DAGnB,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,aAAa,KAAK,EAAE;gDACnC,WAAU;0DAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBArDZ,KAAK,EAAE;;;;;;;;;;;;;;;;AA+D5B;KA7FgB", "debugId": null}}, {"offset": {"line": 769, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/arthamed/analysis-progress.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, CheckCircle2, <PERSON>ader2 } from \"lucide-react\"\r\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from \"@/components/ui/card\"\r\nimport { Progress } from \"@/components/ui/progress\"\r\nimport { Badge } from \"@/components/ui/badge\"\r\n\r\ninterface AnalysisProgressProps {\r\n  progress: number\r\n  stage: string\r\n  fileCount: number\r\n}\r\n\r\nexport function AnalysisProgress({ progress, stage, fileCount }: AnalysisProgressProps) {\r\n  const getStageIcon = () => {\r\n    if (progress < 25) return <FileText className=\"w-5 h-5 text-blue-600 dark:text-blue-400\" />\r\n    if (progress < 75) return <Brain className=\"w-5 h-5 text-purple-600 dark:text-purple-400 animate-pulse\" />\r\n    if (progress < 100) return <Loader2 className=\"w-5 h-5 text-orange-600 dark:text-orange-400 animate-spin\" />\r\n    return <CheckCircle2 className=\"w-5 h-5 text-green-600 dark:text-green-400\" />\r\n  }\r\n\r\n  const getProgressColor = () => {\r\n    if (progress < 25) return \"bg-blue-600 dark:bg-blue-500\"\r\n    if (progress < 50) return \"bg-purple-600 dark:bg-purple-500\"\r\n    if (progress < 75) return \"bg-orange-600 dark:bg-orange-500\"\r\n    return \"bg-green-600 dark:bg-green-500\"\r\n  }\r\n\r\n  return (\r\n    <Card className=\"w-full max-w-md mx-auto shadow-lg border-0 bg-white dark:bg-gray-800 dark:border-gray-700\">\r\n      <CardHeader className=\"text-center pb-4\">\r\n        <CardTitle className=\"flex items-center justify-center space-x-2 dark:text-white\">\r\n          <Brain className=\"w-6 h-6 text-blue-600 dark:text-blue-400\" />\r\n          <span>AI Analysis in Progress</span>\r\n        </CardTitle>\r\n        <div className=\"flex justify-center\">\r\n          <Badge variant=\"secondary\" className=\"text-xs dark:bg-gray-700 dark:text-gray-200\">\r\n            Processing {fileCount} document{fileCount > 1 ? \"s\" : \"\"}\r\n          </Badge>\r\n        </div>\r\n      </CardHeader>\r\n\r\n      <CardContent className=\"space-y-6\">\r\n        {/* Progress Circle */}\r\n        <div className=\"flex justify-center\">\r\n          <div className=\"relative w-24 h-24\">\r\n            <svg className=\"w-24 h-24 transform -rotate-90\" viewBox=\"0 0 100 100\">\r\n              <circle\r\n                cx=\"50\"\r\n                cy=\"50\"\r\n                r=\"40\"\r\n                stroke=\"currentColor\"\r\n                strokeWidth=\"8\"\r\n                fill=\"transparent\"\r\n                className=\"text-gray-200 dark:text-gray-700\"\r\n              />\r\n              <circle\r\n                cx=\"50\"\r\n                cy=\"50\"\r\n                r=\"40\"\r\n                stroke=\"currentColor\"\r\n                strokeWidth=\"8\"\r\n                fill=\"transparent\"\r\n                strokeDasharray={`${2 * Math.PI * 40}`}\r\n                strokeDashoffset={`${2 * Math.PI * 40 * (1 - progress / 100)}`}\r\n                className=\"text-blue-600 dark:text-blue-400 transition-all duration-500 ease-out\"\r\n                strokeLinecap=\"round\"\r\n              />\r\n            </svg>\r\n            <div className=\"absolute inset-0 flex items-center justify-center\">\r\n              <span className=\"text-2xl font-bold text-gray-900 dark:text-white\">{progress}%</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Current Stage */}\r\n        <div className=\"text-center space-y-3\">\r\n          <div className=\"flex items-center justify-center space-x-2\">\r\n            {getStageIcon()}\r\n            <span className=\"font-medium text-gray-900 dark:text-white\">Current Stage</span>\r\n          </div>\r\n          <p className=\"text-sm text-gray-600 dark:text-gray-300 bg-gray-50 dark:bg-gray-700 rounded-lg px-4 py-2\">\r\n            {stage}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Progress Bar */}\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex justify-between text-sm text-gray-600 dark:text-gray-400\">\r\n            <span>Progress</span>\r\n            <span>{progress}% Complete</span>\r\n          </div>\r\n          <div className=\"relative\">\r\n            <Progress \r\n              value={progress} \r\n              className=\"h-3 bg-gray-200 dark:bg-gray-700\" \r\n            />\r\n            <div\r\n              className={`absolute top-0 left-0 h-3 rounded-full transition-all duration-500 ${getProgressColor()}`}\r\n              style={{ width: `${progress}%` }}\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Analysis Steps */}\r\n        <div className=\"space-y-2\">\r\n          <h4 className=\"text-sm font-medium text-gray-900 dark:text-white\">Analysis Steps</h4>\r\n          <div className=\"space-y-1 text-xs\">\r\n            {[\r\n              { threshold: 10, label: \"Document Reading\" },\r\n              { threshold: 25, label: \"Content Processing\" },\r\n              { threshold: 45, label: \"Medical Term Analysis\" },\r\n              { threshold: 65, label: \"Clinical Data Review\" },\r\n              { threshold: 85, label: \"Generating Explanations\" },\r\n              { threshold: 100, label: \"Finalizing Results\" },\r\n            ].map((step) => (\r\n              <div \r\n                key={step.label}\r\n                className={`flex items-center space-x-2 ${\r\n                  progress >= step.threshold \r\n                    ? \"text-green-600 dark:text-green-400\" \r\n                    : \"text-gray-400 dark:text-gray-500\"\r\n                }`}\r\n              >\r\n                <div \r\n                  className={`w-2 h-2 rounded-full ${\r\n                    progress >= step.threshold \r\n                      ? \"bg-green-600 dark:bg-green-400\" \r\n                      : \"bg-gray-300 dark:bg-gray-600\"\r\n                  }`} \r\n                />\r\n                <span>{step.label}</span>\r\n                {progress >= step.threshold && (\r\n                  <CheckCircle2 className=\"w-3 h-3\" />\r\n                )}\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Estimated Time */}\r\n        <div className=\"text-center\">\r\n          <p className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n            {progress < 100 ? (\r\n              <>Estimated time remaining: {Math.ceil((100 - progress) / 20)} minutes</>\r\n            ) : (\r\n              <>Analysis completed successfully!</>\r\n            )}\r\n          </p>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AALA;;;;;;AAaO,SAAS,iBAAiB,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAyB;IACpF,MAAM,eAAe;QACnB,IAAI,WAAW,IAAI,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;QAC9C,IAAI,WAAW,IAAI,qBAAO,6LAAC,uMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAC3C,IAAI,WAAW,KAAK,qBAAO,6LAAC,oNAAA,CAAA,UAAO;YAAC,WAAU;;;;;;QAC9C,qBAAO,6LAAC,wNAAA,CAAA,eAAY;YAAC,WAAU;;;;;;IACjC;IAEA,MAAM,mBAAmB;QACvB,IAAI,WAAW,IAAI,OAAO;QAC1B,IAAI,WAAW,IAAI,OAAO;QAC1B,IAAI,WAAW,IAAI,OAAO;QAC1B,OAAO;IACT;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;0CAAK;;;;;;;;;;;;kCAER,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAY,WAAU;;gCAA8C;gCACrE;gCAAU;gCAAU,YAAY,IAAI,MAAM;;;;;;;;;;;;;;;;;;0BAK5D,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;oCAAiC,SAAQ;;sDACtD,6LAAC;4CACC,IAAG;4CACH,IAAG;4CACH,GAAE;4CACF,QAAO;4CACP,aAAY;4CACZ,MAAK;4CACL,WAAU;;;;;;sDAEZ,6LAAC;4CACC,IAAG;4CACH,IAAG;4CACH,GAAE;4CACF,QAAO;4CACP,aAAY;4CACZ,MAAK;4CACL,iBAAiB,GAAG,IAAI,KAAK,EAAE,GAAG,IAAI;4CACtC,kBAAkB,GAAG,IAAI,KAAK,EAAE,GAAG,KAAK,CAAC,IAAI,WAAW,GAAG,GAAG;4CAC9D,WAAU;4CACV,eAAc;;;;;;;;;;;;8CAGlB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;;4CAAoD;4CAAS;;;;;;;;;;;;;;;;;;;;;;;kCAMnF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;oCACZ;kDACD,6LAAC;wCAAK,WAAU;kDAA4C;;;;;;;;;;;;0CAE9D,6LAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;kCAKL,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAK;;;;;;kDACN,6LAAC;;4CAAM;4CAAS;;;;;;;;;;;;;0CAElB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uIAAA,CAAA,WAAQ;wCACP,OAAO;wCACP,WAAU;;;;;;kDAEZ,6LAAC;wCACC,WAAW,CAAC,mEAAmE,EAAE,oBAAoB;wCACrG,OAAO;4CAAE,OAAO,GAAG,SAAS,CAAC,CAAC;wCAAC;;;;;;;;;;;;;;;;;;kCAMrC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAClE,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,WAAW;wCAAI,OAAO;oCAAmB;oCAC3C;wCAAE,WAAW;wCAAI,OAAO;oCAAqB;oCAC7C;wCAAE,WAAW;wCAAI,OAAO;oCAAwB;oCAChD;wCAAE,WAAW;wCAAI,OAAO;oCAAuB;oCAC/C;wCAAE,WAAW;wCAAI,OAAO;oCAA0B;oCAClD;wCAAE,WAAW;wCAAK,OAAO;oCAAqB;iCAC/C,CAAC,GAAG,CAAC,CAAC,qBACL,6LAAC;wCAEC,WAAW,CAAC,4BAA4B,EACtC,YAAY,KAAK,SAAS,GACtB,uCACA,oCACJ;;0DAEF,6LAAC;gDACC,WAAW,CAAC,qBAAqB,EAC/B,YAAY,KAAK,SAAS,GACtB,mCACA,gCACJ;;;;;;0DAEJ,6LAAC;0DAAM,KAAK,KAAK;;;;;;4CAChB,YAAY,KAAK,SAAS,kBACzB,6LAAC,wNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;uCAhBrB,KAAK,KAAK;;;;;;;;;;;;;;;;kCAwBvB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCACV,WAAW,oBACV;;oCAAE;oCAA2B,KAAK,IAAI,CAAC,CAAC,MAAM,QAAQ,IAAI;oCAAI;;6DAE9D;0CAAE;;;;;;;;;;;;;;;;;;;;;;;;AAOhB;KA5IgB", "debugId": null}}, {"offset": {"line": 1179, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/lib/document-config.ts"], "sourcesContent": ["import { DocumentType } from \"@prisma/client\";\nimport { DocumentTypeConfig, DocumentSystemConfig } from \"@/types/medical-documents\";\n\n// Document type configurations\nexport const DOCUMENT_TYPE_CONFIGS: DocumentTypeConfig[] = [\n  {\n    type: DocumentType.PRESCRIPTION,\n    label: \"Prescription\",\n    description: \"Medical prescriptions and medication orders\",\n    acceptedMimeTypes: [\"image/jpeg\", \"image/png\", \"image/webp\", \"application/pdf\"],\n    maxSize: 10 * 1024 * 1024, // 10MB\n    icon: \"pill\",\n    analysisPrompt: `\n      Analyze this prescription document and extract:\n      - Patient information (name, age, gender)\n      - Prescribed medications with dosages\n      - Doctor information\n      - Date of prescription\n      - Any special instructions\n      - Identify potential drug interactions or concerns\n    `\n  },\n  {\n    type: DocumentType.LAB_REPORT,\n    label: \"Lab Report\",\n    description: \"Laboratory test results and pathology reports\",\n    acceptedMimeTypes: [\"image/jpeg\", \"image/png\", \"image/webp\", \"application/pdf\"],\n    maxSize: 15 * 1024 * 1024, // 15MB\n    icon: \"test-tube\",\n    analysisPrompt: `\n      Analyze this lab report and extract:\n      - Patient information\n      - Test types and results\n      - Reference ranges and abnormal values\n      - Test dates\n      - Recommendations or follow-up needed\n      - Urgency level based on critical values\n    `\n  },\n  {\n    type: DocumentType.MRI_REPORT,\n    label: \"MRI Report\",\n    description: \"MRI scan results and radiological reports\",\n    acceptedMimeTypes: [\"image/jpeg\", \"image/png\", \"image/webp\", \"application/pdf\"],\n    maxSize: 20 * 1024 * 1024, // 20MB\n    icon: \"brain\",\n    analysisPrompt: `\n      Analyze this MRI report and extract:\n      - Patient information\n      - Body part/region scanned\n      - Key findings and abnormalities\n      - Radiologist recommendations\n      - Urgency level\n      - Suggested follow-up or specialist referral\n    `\n  },\n  {\n    type: DocumentType.XRAY_REPORT,\n    label: \"X-Ray Report\",\n    description: \"X-ray images and radiological interpretations\",\n    acceptedMimeTypes: [\"image/jpeg\", \"image/png\", \"image/webp\", \"application/pdf\"],\n    maxSize: 15 * 1024 * 1024, // 15MB\n    icon: \"x-ray\",\n    analysisPrompt: `\n      Analyze this X-ray report and extract:\n      - Patient information\n      - Body part examined\n      - Findings and abnormalities\n      - Fractures, infections, or other conditions\n      - Recommendations for treatment\n      - Need for urgent care\n    `\n  },\n  {\n    type: DocumentType.DISCHARGE_SUMMARY,\n    label: \"Discharge Summary\",\n    description: \"Hospital discharge summaries and care instructions\",\n    acceptedMimeTypes: [\"image/jpeg\", \"image/png\", \"image/webp\", \"application/pdf\"],\n    maxSize: 10 * 1024 * 1024, // 10MB\n    icon: \"file-text\",\n    analysisPrompt: `\n      Analyze this discharge summary and extract:\n      - Patient information\n      - Admission and discharge dates\n      - Primary and secondary diagnoses\n      - Procedures performed\n      - Medications prescribed\n      - Follow-up instructions\n      - Warning signs to watch for\n    `\n  },\n  {\n    type: DocumentType.MEDICAL_CERTIFICATE,\n    label: \"Medical Certificate\",\n    description: \"Medical certificates and fitness reports\",\n    acceptedMimeTypes: [\"image/jpeg\", \"image/png\", \"image/webp\", \"application/pdf\"],\n    maxSize: 5 * 1024 * 1024, // 5MB\n    icon: \"certificate\",\n    analysisPrompt: `\n      Analyze this medical certificate and extract:\n      - Patient information\n      - Medical condition or fitness status\n      - Validity period\n      - Restrictions or recommendations\n      - Doctor information and signature\n    `\n  },\n  {\n    type: DocumentType.OTHER,\n    label: \"Other Medical Document\",\n    description: \"Other medical documents and reports\",\n    acceptedMimeTypes: [\"image/jpeg\", \"image/png\", \"image/webp\", \"application/pdf\"],\n    maxSize: 10 * 1024 * 1024, // 10MB\n    icon: \"file\",\n    analysisPrompt: `\n      Analyze this medical document and extract:\n      - Document type and purpose\n      - Patient information\n      - Key medical information\n      - Dates and healthcare provider details\n      - Any recommendations or follow-up needed\n    `\n  }\n];\n\n// System configuration\nexport const DOCUMENT_SYSTEM_CONFIG: DocumentSystemConfig = {\n  maxFileSize: 20 * 1024 * 1024, // 20MB max\n  maxFilesPerUpload: 10,\n  supportedTypes: DOCUMENT_TYPE_CONFIGS,\n  storageProvider: \"local\", // Can be configured via env\n  analysisProvider: \"gemini\",\n  autoAnalysis: true,\n  retentionPeriod: 365, // 1 year\n};\n\n// Accepted file types for dropzone\nexport const ACCEPTED_FILE_TYPES = {\n  \"image/jpeg\": [\".jpg\", \".jpeg\"],\n  \"image/png\": [\".png\"],\n  \"image/webp\": [\".webp\"],\n  \"application/pdf\": [\".pdf\"],\n};\n\n// File type validation\nexport function validateFileType(file: File): boolean {\n  return Object.keys(ACCEPTED_FILE_TYPES).includes(file.type);\n}\n\n// File size validation\nexport function validateFileSize(file: File, maxSize?: number): boolean {\n  const limit = maxSize || DOCUMENT_SYSTEM_CONFIG.maxFileSize;\n  return file.size <= limit;\n}\n\n// Get document type config\nexport function getDocumentTypeConfig(type: DocumentType): DocumentTypeConfig | undefined {\n  return DOCUMENT_TYPE_CONFIGS.find(config => config.type === type);\n}\n\n// Format file size for display\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return \"0 Bytes\";\n  const k = 1024;\n  const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\"];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\n\n// Get file extension\nexport function getFileExtension(filename: string): string {\n  return filename.slice((filename.lastIndexOf(\".\") - 1 >>> 0) + 2);\n}\n\n// Generate unique filename\nexport function generateUniqueFilename(originalName: string): string {\n  const timestamp = Date.now();\n  const random = Math.random().toString(36).substring(2, 15);\n  const extension = getFileExtension(originalName);\n  return `${timestamp}_${random}.${extension}`;\n}\n\n// Check if file is an image\nexport function isImageFile(mimeType: string): boolean {\n  return mimeType.startsWith(\"image/\");\n}\n\n// Check if file is a PDF\nexport function isPdfFile(mimeType: string): boolean {\n  return mimeType === \"application/pdf\";\n}\n\n// Get appropriate icon for file type\nexport function getFileTypeIcon(mimeType: string): string {\n  if (isImageFile(mimeType)) return \"image\";\n  if (isPdfFile(mimeType)) return \"file-text\";\n  return \"file\";\n}\n\n// Document type display labels\nexport const DOCUMENT_TYPE_LABELS: Record<DocumentType, string> = {\n  [DocumentType.PRESCRIPTION]: \"Prescription\",\n  [DocumentType.LAB_REPORT]: \"Lab Report\",\n  [DocumentType.MRI_REPORT]: \"MRI Report\",\n  [DocumentType.XRAY_REPORT]: \"X-Ray Report\",\n  [DocumentType.DISCHARGE_SUMMARY]: \"Discharge Summary\",\n  [DocumentType.MEDICAL_CERTIFICATE]: \"Medical Certificate\",\n  [DocumentType.OTHER]: \"Other Document\",\n};\n\n// Priority levels for document processing\nexport const DOCUMENT_PRIORITY_LEVELS = {\n  CRITICAL: { value: 1, label: \"Critical\", color: \"red\" },\n  HIGH: { value: 2, label: \"High\", color: \"orange\" },\n  NORMAL: { value: 3, label: \"Normal\", color: \"blue\" },\n  LOW: { value: 4, label: \"Low\", color: \"gray\" },\n};\n\n// Analysis stage descriptions\nexport const ANALYSIS_STAGES = [\n  { threshold: 10, label: \"Document Reading\", description: \"Reading and parsing document content\" },\n  { threshold: 25, label: \"Content Processing\", description: \"Processing text and image content\" },\n  { threshold: 45, label: \"Medical Term Analysis\", description: \"Analyzing medical terminology and context\" },\n  { threshold: 65, label: \"Clinical Data Review\", description: \"Reviewing clinical data and findings\" },\n  { threshold: 85, label: \"Generating Recommendations\", description: \"Generating recommendations and routing suggestions\" },\n  { threshold: 100, label: \"Finalizing Results\", description: \"Finalizing analysis results\" },\n];\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;;AAIO,MAAM,wBAA8C;IACzD;QACE,MAAM,yJAAA,CAAA,eAAY,CAAC,YAAY;QAC/B,OAAO;QACP,aAAa;QACb,mBAAmB;YAAC;YAAc;YAAa;YAAc;SAAkB;QAC/E,SAAS,KAAK,OAAO;QACrB,MAAM;QACN,gBAAgB,CAAC;;;;;;;;IAQjB,CAAC;IACH;IACA;QACE,MAAM,yJAAA,CAAA,eAAY,CAAC,UAAU;QAC7B,OAAO;QACP,aAAa;QACb,mBAAmB;YAAC;YAAc;YAAa;YAAc;SAAkB;QAC/E,SAAS,KAAK,OAAO;QACrB,MAAM;QACN,gBAAgB,CAAC;;;;;;;;IAQjB,CAAC;IACH;IACA;QACE,MAAM,yJAAA,CAAA,eAAY,CAAC,UAAU;QAC7B,OAAO;QACP,aAAa;QACb,mBAAmB;YAAC;YAAc;YAAa;YAAc;SAAkB;QAC/E,SAAS,KAAK,OAAO;QACrB,MAAM;QACN,gBAAgB,CAAC;;;;;;;;IAQjB,CAAC;IACH;IACA;QACE,MAAM,yJAAA,CAAA,eAAY,CAAC,WAAW;QAC9B,OAAO;QACP,aAAa;QACb,mBAAmB;YAAC;YAAc;YAAa;YAAc;SAAkB;QAC/E,SAAS,KAAK,OAAO;QACrB,MAAM;QACN,gBAAgB,CAAC;;;;;;;;IAQjB,CAAC;IACH;IACA;QACE,MAAM,yJAAA,CAAA,eAAY,CAAC,iBAAiB;QACpC,OAAO;QACP,aAAa;QACb,mBAAmB;YAAC;YAAc;YAAa;YAAc;SAAkB;QAC/E,SAAS,KAAK,OAAO;QACrB,MAAM;QACN,gBAAgB,CAAC;;;;;;;;;IASjB,CAAC;IACH;IACA;QACE,MAAM,yJAAA,CAAA,eAAY,CAAC,mBAAmB;QACtC,OAAO;QACP,aAAa;QACb,mBAAmB;YAAC;YAAc;YAAa;YAAc;SAAkB;QAC/E,SAAS,IAAI,OAAO;QACpB,MAAM;QACN,gBAAgB,CAAC;;;;;;;IAOjB,CAAC;IACH;IACA;QACE,MAAM,yJAAA,CAAA,eAAY,CAAC,KAAK;QACxB,OAAO;QACP,aAAa;QACb,mBAAmB;YAAC;YAAc;YAAa;YAAc;SAAkB;QAC/E,SAAS,KAAK,OAAO;QACrB,MAAM;QACN,gBAAgB,CAAC;;;;;;;IAOjB,CAAC;IACH;CACD;AAGM,MAAM,yBAA+C;IAC1D,aAAa,KAAK,OAAO;IACzB,mBAAmB;IACnB,gBAAgB;IAChB,iBAAiB;IACjB,kBAAkB;IAClB,cAAc;IACd,iBAAiB;AACnB;AAGO,MAAM,sBAAsB;IACjC,cAAc;QAAC;QAAQ;KAAQ;IAC/B,aAAa;QAAC;KAAO;IACrB,cAAc;QAAC;KAAQ;IACvB,mBAAmB;QAAC;KAAO;AAC7B;AAGO,SAAS,iBAAiB,IAAU;IACzC,OAAO,OAAO,IAAI,CAAC,qBAAqB,QAAQ,CAAC,KAAK,IAAI;AAC5D;AAGO,SAAS,iBAAiB,IAAU,EAAE,OAAgB;IAC3D,MAAM,QAAQ,WAAW,uBAAuB,WAAW;IAC3D,OAAO,KAAK,IAAI,IAAI;AACtB;AAGO,SAAS,sBAAsB,IAAkB;IACtD,OAAO,sBAAsB,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;AAC9D;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,OAAO,UAAU,CAAC,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AAChF;AAGO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SAAS,KAAK,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO,MAAM,CAAC,IAAI;AAChE;AAGO,SAAS,uBAAuB,YAAoB;IACzD,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IACvD,MAAM,YAAY,iBAAiB;IACnC,OAAO,GAAG,UAAU,CAAC,EAAE,OAAO,CAAC,EAAE,WAAW;AAC9C;AAGO,SAAS,YAAY,QAAgB;IAC1C,OAAO,SAAS,UAAU,CAAC;AAC7B;AAGO,SAAS,UAAU,QAAgB;IACxC,OAAO,aAAa;AACtB;AAGO,SAAS,gBAAgB,QAAgB;IAC9C,IAAI,YAAY,WAAW,OAAO;IAClC,IAAI,UAAU,WAAW,OAAO;IAChC,OAAO;AACT;AAGO,MAAM,uBAAqD;IAChE,CAAC,yJAAA,CAAA,eAAY,CAAC,YAAY,CAAC,EAAE;IAC7B,CAAC,yJAAA,CAAA,eAAY,CAAC,UAAU,CAAC,EAAE;IAC3B,CAAC,yJAAA,CAAA,eAAY,CAAC,UAAU,CAAC,EAAE;IAC3B,CAAC,yJAAA,CAAA,eAAY,CAAC,WAAW,CAAC,EAAE;IAC5B,CAAC,yJAAA,CAAA,eAAY,CAAC,iBAAiB,CAAC,EAAE;IAClC,CAAC,yJAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC,EAAE;IACpC,CAAC,yJAAA,CAAA,eAAY,CAAC,KAAK,CAAC,EAAE;AACxB;AAGO,MAAM,2BAA2B;IACtC,UAAU;QAAE,OAAO;QAAG,OAAO;QAAY,OAAO;IAAM;IACtD,MAAM;QAAE,OAAO;QAAG,OAAO;QAAQ,OAAO;IAAS;IACjD,QAAQ;QAAE,OAAO;QAAG,OAAO;QAAU,OAAO;IAAO;IACnD,KAAK;QAAE,OAAO;QAAG,OAAO;QAAO,OAAO;IAAO;AAC/C;AAGO,MAAM,kBAAkB;IAC7B;QAAE,WAAW;QAAI,OAAO;QAAoB,aAAa;IAAuC;IAChG;QAAE,WAAW;QAAI,OAAO;QAAsB,aAAa;IAAoC;IAC/F;QAAE,WAAW;QAAI,OAAO;QAAyB,aAAa;IAA4C;IAC1G;QAAE,WAAW;QAAI,OAAO;QAAwB,aAAa;IAAuC;IACpG;QAAE,WAAW;QAAI,OAAO;QAA8B,aAAa;IAAqD;IACxH;QAAE,WAAW;QAAK,OAAO;QAAsB,aAAa;IAA8B;CAC3F", "debugId": null}}, {"offset": {"line": 1491, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/types/doctors.ts"], "sourcesContent": ["import { Doctor<PERSON><PERSON><PERSON><PERSON> } from \"@prisma/client\";\r\n\r\nexport type Doctor = {\r\n  id: string;\r\n  name: string;\r\n  email: string;\r\n  specialty?: DoctorSpec<PERSON><PERSON>;\r\n  licenseNumber?: string;\r\n  yearsOfExperience?: number;\r\n  bio?: string;\r\n  consultationFee?: number;\r\n};\r\n\r\n// Extended doctor type with additional computed fields\r\nexport type EnhancedDoctor = Doctor & {\r\n  rating?: number;\r\n  availableSlots?: Date[];\r\n  isAvailable?: boolean;\r\n  patientCount?: number;\r\n  nextAvailableSlot?: Date;\r\n};\r\n\r\n// Doctor specialty mapping for display\r\nexport const DoctorSpecialtyLabels: Record<DoctorSpecialty, string> = {\r\n  GENERAL_MEDICINE: \"General Medicine\",\r\n  CARDIOLOGY: \"Cardiology\",\r\n  NEUROLOGY: \"Neurology\",\r\n  ORTHOPEDICS: \"Orthopedics\",\r\n  DERMATOLOGY: \"Dermatology\",\r\n  PEDIATRICS: \"Pediatrics\",\r\n  GYNECOLOGY: \"Gynecology\",\r\n  PSYCHIATRY: \"Psychiatry\",\r\n  RADIOLOGY: \"Radiology\",\r\n  PATHOLOGY: \"Pathology\",\r\n  ONCOLOGY: \"Oncology\",\r\n  ENDOCRINOLOGY: \"Endocrinology\",\r\n  GASTROENTEROLOGY: \"Gastroenterology\",\r\n  PULMONOLOGY: \"Pulmonology\",\r\n  NEPHROLOGY: \"Nephrology\",\r\n  OPHTHALMOLOGY: \"Ophthalmology\",\r\n  ENT: \"ENT (Ear, Nose, Throat)\",\r\n  ANESTHESIOLOGY: \"Anesthesiology\",\r\n  EMERGENCY_MEDICINE: \"Emergency Medicine\",\r\n  FAMILY_MEDICINE: \"Family Medicine\",\r\n};\r\n\r\n// Specialty to condition mapping for smart routing\r\nexport const SpecialtyConditionMapping: Record<string, DoctorSpecialty[]> = {\r\n  // Cardiovascular conditions\r\n  heart: [DoctorSpecialty.CARDIOLOGY, DoctorSpecialty.GENERAL_MEDICINE],\r\n  cardiac: [DoctorSpecialty.CARDIOLOGY],\r\n  hypertension: [DoctorSpecialty.CARDIOLOGY, DoctorSpecialty.GENERAL_MEDICINE],\r\n  \"chest pain\": [\r\n    DoctorSpecialty.CARDIOLOGY,\r\n    DoctorSpecialty.EMERGENCY_MEDICINE,\r\n  ],\r\n\r\n  // Neurological conditions\r\n  headache: [DoctorSpecialty.NEUROLOGY, DoctorSpecialty.GENERAL_MEDICINE],\r\n  migraine: [DoctorSpecialty.NEUROLOGY],\r\n  seizure: [DoctorSpecialty.NEUROLOGY, DoctorSpecialty.EMERGENCY_MEDICINE],\r\n  stroke: [DoctorSpecialty.NEUROLOGY, DoctorSpecialty.EMERGENCY_MEDICINE],\r\n  dementia: [DoctorSpecialty.NEUROLOGY, DoctorSpecialty.PSYCHIATRY],\r\n\r\n  // Orthopedic conditions\r\n  fracture: [DoctorSpecialty.ORTHOPEDICS, DoctorSpecialty.EMERGENCY_MEDICINE],\r\n  \"joint pain\": [DoctorSpecialty.ORTHOPEDICS, DoctorSpecialty.GENERAL_MEDICINE],\r\n  arthritis: [DoctorSpecialty.ORTHOPEDICS],\r\n  \"back pain\": [DoctorSpecialty.ORTHOPEDICS, DoctorSpecialty.GENERAL_MEDICINE],\r\n\r\n  // Skin conditions\r\n  rash: [DoctorSpecialty.DERMATOLOGY, DoctorSpecialty.GENERAL_MEDICINE],\r\n  acne: [DoctorSpecialty.DERMATOLOGY],\r\n  eczema: [DoctorSpecialty.DERMATOLOGY],\r\n  psoriasis: [DoctorSpecialty.DERMATOLOGY],\r\n\r\n  // Respiratory conditions\r\n  cough: [DoctorSpecialty.PULMONOLOGY, DoctorSpecialty.GENERAL_MEDICINE],\r\n  asthma: [DoctorSpecialty.PULMONOLOGY],\r\n  pneumonia: [DoctorSpecialty.PULMONOLOGY, DoctorSpecialty.GENERAL_MEDICINE],\r\n  \"shortness of breath\": [\r\n    DoctorSpecialty.PULMONOLOGY,\r\n    DoctorSpecialty.CARDIOLOGY,\r\n  ],\r\n\r\n  // Digestive conditions\r\n  \"stomach pain\": [\r\n    DoctorSpecialty.GASTROENTEROLOGY,\r\n    DoctorSpecialty.GENERAL_MEDICINE,\r\n  ],\r\n  nausea: [DoctorSpecialty.GASTROENTEROLOGY, DoctorSpecialty.GENERAL_MEDICINE],\r\n  diarrhea: [\r\n    DoctorSpecialty.GASTROENTEROLOGY,\r\n    DoctorSpecialty.GENERAL_MEDICINE,\r\n  ],\r\n  constipation: [\r\n    DoctorSpecialty.GASTROENTEROLOGY,\r\n    DoctorSpecialty.GENERAL_MEDICINE,\r\n  ],\r\n\r\n  // Mental health conditions\r\n  depression: [DoctorSpecialty.PSYCHIATRY, DoctorSpecialty.GENERAL_MEDICINE],\r\n  anxiety: [DoctorSpecialty.PSYCHIATRY, DoctorSpecialty.GENERAL_MEDICINE],\r\n  insomnia: [DoctorSpecialty.PSYCHIATRY, DoctorSpecialty.GENERAL_MEDICINE],\r\n\r\n  // Endocrine conditions\r\n  diabetes: [DoctorSpecialty.ENDOCRINOLOGY, DoctorSpecialty.GENERAL_MEDICINE],\r\n  thyroid: [DoctorSpecialty.ENDOCRINOLOGY],\r\n  hormone: [DoctorSpecialty.ENDOCRINOLOGY, DoctorSpecialty.GYNECOLOGY],\r\n\r\n  // Eye conditions\r\n  vision: [DoctorSpecialty.OPHTHALMOLOGY],\r\n  \"eye pain\": [DoctorSpecialty.OPHTHALMOLOGY],\r\n  \"blurred vision\": [DoctorSpecialty.OPHTHALMOLOGY, DoctorSpecialty.NEUROLOGY],\r\n\r\n  // General conditions\r\n  fever: [DoctorSpecialty.GENERAL_MEDICINE, DoctorSpecialty.EMERGENCY_MEDICINE],\r\n  fatigue: [DoctorSpecialty.GENERAL_MEDICINE],\r\n  \"weight loss\": [DoctorSpecialty.GENERAL_MEDICINE, DoctorSpecialty.ONCOLOGY],\r\n  \"weight gain\": [\r\n    DoctorSpecialty.GENERAL_MEDICINE,\r\n    DoctorSpecialty.ENDOCRINOLOGY,\r\n  ],\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;AAuBO,MAAM,wBAAyD;IACpE,kBAAkB;IAClB,YAAY;IACZ,WAAW;IACX,aAAa;IACb,aAAa;IACb,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,WAAW;IACX,WAAW;IACX,UAAU;IACV,eAAe;IACf,kBAAkB;IAClB,aAAa;IACb,YAAY;IACZ,eAAe;IACf,KAAK;IACL,gBAAgB;IAChB,oBAAoB;IACpB,iBAAiB;AACnB;AAGO,MAAM,4BAA+D;IAC1E,4BAA4B;IAC5B,OAAO;QAAC,yJAAA,CAAA,kBAAe,CAAC,UAAU;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IACrE,SAAS;QAAC,yJAAA,CAAA,kBAAe,CAAC,UAAU;KAAC;IACrC,cAAc;QAAC,yJAAA,CAAA,kBAAe,CAAC,UAAU;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IAC5E,cAAc;QACZ,yJAAA,CAAA,kBAAe,CAAC,UAAU;QAC1B,yJAAA,CAAA,kBAAe,CAAC,kBAAkB;KACnC;IAED,0BAA0B;IAC1B,UAAU;QAAC,yJAAA,CAAA,kBAAe,CAAC,SAAS;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IACvE,UAAU;QAAC,yJAAA,CAAA,kBAAe,CAAC,SAAS;KAAC;IACrC,SAAS;QAAC,yJAAA,CAAA,kBAAe,CAAC,SAAS;QAAE,yJAAA,CAAA,kBAAe,CAAC,kBAAkB;KAAC;IACxE,QAAQ;QAAC,yJAAA,CAAA,kBAAe,CAAC,SAAS;QAAE,yJAAA,CAAA,kBAAe,CAAC,kBAAkB;KAAC;IACvE,UAAU;QAAC,yJAAA,CAAA,kBAAe,CAAC,SAAS;QAAE,yJAAA,CAAA,kBAAe,CAAC,UAAU;KAAC;IAEjE,wBAAwB;IACxB,UAAU;QAAC,yJAAA,CAAA,kBAAe,CAAC,WAAW;QAAE,yJAAA,CAAA,kBAAe,CAAC,kBAAkB;KAAC;IAC3E,cAAc;QAAC,yJAAA,CAAA,kBAAe,CAAC,WAAW;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IAC7E,WAAW;QAAC,yJAAA,CAAA,kBAAe,CAAC,WAAW;KAAC;IACxC,aAAa;QAAC,yJAAA,CAAA,kBAAe,CAAC,WAAW;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IAE5E,kBAAkB;IAClB,MAAM;QAAC,yJAAA,CAAA,kBAAe,CAAC,WAAW;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IACrE,MAAM;QAAC,yJAAA,CAAA,kBAAe,CAAC,WAAW;KAAC;IACnC,QAAQ;QAAC,yJAAA,CAAA,kBAAe,CAAC,WAAW;KAAC;IACrC,WAAW;QAAC,yJAAA,CAAA,kBAAe,CAAC,WAAW;KAAC;IAExC,yBAAyB;IACzB,OAAO;QAAC,yJAAA,CAAA,kBAAe,CAAC,WAAW;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IACtE,QAAQ;QAAC,yJAAA,CAAA,kBAAe,CAAC,WAAW;KAAC;IACrC,WAAW;QAAC,yJAAA,CAAA,kBAAe,CAAC,WAAW;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IAC1E,uBAAuB;QACrB,yJAAA,CAAA,kBAAe,CAAC,WAAW;QAC3B,yJAAA,CAAA,kBAAe,CAAC,UAAU;KAC3B;IAED,uBAAuB;IACvB,gBAAgB;QACd,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;QAChC,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KACjC;IACD,QAAQ;QAAC,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IAC5E,UAAU;QACR,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;QAChC,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KACjC;IACD,cAAc;QACZ,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;QAChC,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KACjC;IAED,2BAA2B;IAC3B,YAAY;QAAC,yJAAA,CAAA,kBAAe,CAAC,UAAU;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IAC1E,SAAS;QAAC,yJAAA,CAAA,kBAAe,CAAC,UAAU;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IACvE,UAAU;QAAC,yJAAA,CAAA,kBAAe,CAAC,UAAU;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IAExE,uBAAuB;IACvB,UAAU;QAAC,yJAAA,CAAA,kBAAe,CAAC,aAAa;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IAC3E,SAAS;QAAC,yJAAA,CAAA,kBAAe,CAAC,aAAa;KAAC;IACxC,SAAS;QAAC,yJAAA,CAAA,kBAAe,CAAC,aAAa;QAAE,yJAAA,CAAA,kBAAe,CAAC,UAAU;KAAC;IAEpE,iBAAiB;IACjB,QAAQ;QAAC,yJAAA,CAAA,kBAAe,CAAC,aAAa;KAAC;IACvC,YAAY;QAAC,yJAAA,CAAA,kBAAe,CAAC,aAAa;KAAC;IAC3C,kBAAkB;QAAC,yJAAA,CAAA,kBAAe,CAAC,aAAa;QAAE,yJAAA,CAAA,kBAAe,CAAC,SAAS;KAAC;IAE5E,qBAAqB;IACrB,OAAO;QAAC,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;QAAE,yJAAA,CAAA,kBAAe,CAAC,kBAAkB;KAAC;IAC7E,SAAS;QAAC,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IAC3C,eAAe;QAAC,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;QAAE,yJAAA,CAAA,kBAAe,CAAC,QAAQ;KAAC;IAC3E,eAAe;QACb,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;QAChC,yJAAA,CAAA,kBAAe,CAAC,aAAa;KAC9B;AACH", "debugId": null}}, {"offset": {"line": 1681, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/lib/gemini.ts"], "sourcesContent": ["import { GoogleGenAI } from \"@google/genai\";\r\nimport { DocumentType, DoctorSpecialty, UrgencyLevel } from \"@prisma/client\";\r\nimport { GeminiAnalysisResponse } from \"@/types/medical-documents\";\r\nimport { getDocumentTypeConfig } from \"@/lib/document-config\";\r\nimport { SpecialtyConditionMapping } from \"@/types/doctors\";\r\n\r\nconst ai = new GoogleGenAI({\r\n  apiKey: process.env.NEXT_PUBLIC_GEMINI_API_KEY!,\r\n});\r\n\r\n// Enhanced medical document analysis with document type-specific prompts\r\nexport async function analyzeMedicalDocument(\r\n  base64: string,\r\n  mimeType: string,\r\n  documentType: DocumentType\r\n): Promise<GeminiAnalysisResponse> {\r\n  const config = getDocumentTypeConfig(documentType);\r\n  const customPrompt = config?.analysisPrompt || getDefaultAnalysisPrompt();\r\n\r\n  const result = await ai.models.generateContent({\r\n    model: \"gemini-1.5-flash\",\r\n    contents: [\r\n      {\r\n        inlineData: {\r\n          data: base64,\r\n          mimeType,\r\n        },\r\n      },\r\n      {\r\n        text: `\r\nYou are an advanced medical document analysis AI. Analyze the uploaded ${documentType\r\n          .toLowerCase()\r\n          .replace(\"_\", \" \")} document.\r\n\r\n${customPrompt}\r\n\r\nExtract structured data in the following **strict JSON format**:\r\n\r\n{\r\n  \"document_type\": \"Prescription | Lab Report | MRI Report | X-Ray Report | Discharge Summary | Medical Certificate | Other\",\r\n  \"patient\": {\r\n    \"name\": \"Full Name (if available)\",\r\n    \"age\": 45,\r\n    \"gender\": \"Male\" // or \"Female\" or \"Other\"\r\n  },\r\n  \"symptoms\": [\"fever\", \"headache\", \"nausea\"],\r\n  \"medications\": [\"paracetamol 500mg\", \"ondansetron\"],\r\n  \"diagnoses_or_keywords\": [\"Pneumonia\", \"Mild Dementia\"],\r\n  \"suggested_tests\": [\"MRI Brain\", \"Blood CBC\"],\r\n  \"recommended_specialists\": [\"Neurologist\", \"Pulmonologist\", \"Cardiologist\"],\r\n  \"urgency\": \"Low | Medium | High\"\r\n}\r\n\r\nIMPORTANT GUIDELINES:\r\n1. For urgency assessment:\r\n   - \"High\": Critical conditions, emergency symptoms, abnormal vital signs\r\n   - \"Medium\": Concerning findings that need prompt attention\r\n   - \"Low\": Routine follow-ups, stable conditions\r\n2. For recommended_specialists, use these exact terms:\r\n   - \"General Medicine\", \"Cardiology\", \"Neurology\", \"Orthopedics\", \"Dermatology\"\r\n   - \"Pediatrics\", \"Gynecology\", \"Psychiatry\", \"Radiology\", \"Pathology\"\r\n   - \"Oncology\", \"Endocrinology\", \"Gastroenterology\", \"Pulmonology\"\r\n   - \"Nephrology\", \"Ophthalmology\", \"ENT\", \"Emergency Medicine\"\r\n3. Extract ALL medications with dosages if visible\r\n4. Include ALL symptoms mentioned or implied\r\n5. List ALL diagnoses, conditions, or medical keywords found\r\n\r\nOnly return valid JSON without any additional explanation or text. If any data is missing or illegible, leave fields empty or null but maintain the structure.\r\n        `,\r\n      },\r\n    ],\r\n  });\r\n\r\n  // Extract and parse JSON response\r\n  const raw = result.text?.trim() ?? \"\";\r\n  const jsonMatch = raw.match(/\\{[\\s\\S]*\\}/);\r\n  const jsonString = jsonMatch?.[0] ?? \"{}\";\r\n\r\n  try {\r\n    return JSON.parse(jsonString) as GeminiAnalysisResponse;\r\n  } catch (error) {\r\n    console.error(\"Failed to parse Gemini response:\", error);\r\n    throw new Error(\"Failed to parse AI analysis response\");\r\n  }\r\n}\r\n\r\n// Legacy function for backward compatibility\r\nexport async function analyzeMedicalImage(base64: string, mimeType: string) {\r\n  const analysis = await analyzeMedicalDocument(\r\n    base64,\r\n    mimeType,\r\n    DocumentType.OTHER\r\n  );\r\n  return JSON.stringify(analysis);\r\n}\r\n\r\n// Batch analysis for multiple documents\r\nexport async function batchAnalyzeMedicalDocuments(\r\n  documents: Array<{\r\n    base64: string;\r\n    mimeType: string;\r\n    documentType: DocumentType;\r\n    fileName: string;\r\n  }>\r\n): Promise<\r\n  Array<{ fileName: string; analysis: GeminiAnalysisResponse; error?: string }>\r\n> {\r\n  const results = await Promise.allSettled(\r\n    documents.map(async (doc) => {\r\n      const analysis = await analyzeMedicalDocument(\r\n        doc.base64,\r\n        doc.mimeType,\r\n        doc.documentType\r\n      );\r\n      return { fileName: doc.fileName, analysis };\r\n    })\r\n  );\r\n\r\n  return results.map((result, index) => {\r\n    if (result.status === \"fulfilled\") {\r\n      return result.value;\r\n    } else {\r\n      return {\r\n        fileName: documents[index].fileName,\r\n        analysis: getEmptyAnalysis(),\r\n        error: result.reason?.message || \"Analysis failed\",\r\n      };\r\n    }\r\n  });\r\n}\r\n\r\n// Smart routing based on analysis results\r\nexport function generateSmartRouting(analysis: GeminiAnalysisResponse): {\r\n  recommendedSpecialties: DoctorSpecialty[];\r\n  urgencyLevel: UrgencyLevel;\r\n  routingReason: string;\r\n} {\r\n  const specialties = new Set<DoctorSpecialty>();\r\n  const reasons: string[] = [];\r\n\r\n  // Map urgency\r\n  const urgencyLevel = mapUrgencyLevel(analysis.urgency);\r\n\r\n  // Add specialties based on recommended specialists from AI\r\n  analysis.recommended_specialists.forEach((specialist) => {\r\n    const specialty = mapSpecialistToSpecialty(specialist);\r\n    if (specialty) {\r\n      specialties.add(specialty);\r\n      reasons.push(`AI recommended ${specialist}`);\r\n    }\r\n  });\r\n\r\n  // Add specialties based on symptoms and diagnoses\r\n  const allConditions = [\r\n    ...analysis.symptoms,\r\n    ...analysis.diagnoses_or_keywords,\r\n  ].map((item) => item.toLowerCase());\r\n\r\n  allConditions.forEach((condition) => {\r\n    Object.entries(SpecialtyConditionMapping).forEach(\r\n      ([keyword, relatedSpecialties]) => {\r\n        if (condition.includes(keyword.toLowerCase())) {\r\n          relatedSpecialties.forEach((specialty) => {\r\n            specialties.add(specialty);\r\n            reasons.push(`Condition \"${condition}\" suggests ${specialty}`);\r\n          });\r\n        }\r\n      }\r\n    );\r\n  });\r\n\r\n  // Ensure at least General Medicine is included\r\n  if (specialties.size === 0) {\r\n    specialties.add(DoctorSpecialty.GENERAL_MEDICINE);\r\n    reasons.push(\"Default routing to General Medicine\");\r\n  }\r\n\r\n  return {\r\n    recommendedSpecialties: Array.from(specialties),\r\n    urgencyLevel,\r\n    routingReason: reasons.join(\"; \"),\r\n  };\r\n}\r\n\r\n// Helper functions\r\nfunction getDefaultAnalysisPrompt(): string {\r\n  return `\r\nAnalyze this medical document and extract:\r\n- Patient demographic information\r\n- Medical conditions, symptoms, and diagnoses\r\n- Medications and treatments\r\n- Test results and recommendations\r\n- Urgency level and follow-up needs\r\n  `;\r\n}\r\n\r\nfunction getEmptyAnalysis(): GeminiAnalysisResponse {\r\n  return {\r\n    document_type: \"Other\",\r\n    patient: {\r\n      name: undefined,\r\n      age: undefined,\r\n      gender: undefined,\r\n    },\r\n    symptoms: [],\r\n    medications: [],\r\n    diagnoses_or_keywords: [],\r\n    suggested_tests: [],\r\n    recommended_specialists: [],\r\n    urgency: \"Low\",\r\n  };\r\n}\r\n\r\nfunction mapUrgencyLevel(urgency: string): UrgencyLevel {\r\n  switch (urgency.toLowerCase()) {\r\n    case \"high\":\r\n    case \"critical\":\r\n      return UrgencyLevel.HIGH;\r\n    case \"medium\":\r\n    case \"moderate\":\r\n      return UrgencyLevel.MEDIUM;\r\n    case \"low\":\r\n    default:\r\n      return UrgencyLevel.LOW;\r\n  }\r\n}\r\n\r\nfunction mapSpecialistToSpecialty(specialist: string): DoctorSpecialty | null {\r\n  const mapping: Record<string, DoctorSpecialty> = {\r\n    \"general medicine\": DoctorSpecialty.GENERAL_MEDICINE,\r\n    cardiology: DoctorSpecialty.CARDIOLOGY,\r\n    cardiologist: DoctorSpecialty.CARDIOLOGY,\r\n    neurology: DoctorSpecialty.NEUROLOGY,\r\n    neurologist: DoctorSpecialty.NEUROLOGY,\r\n    orthopedics: DoctorSpecialty.ORTHOPEDICS,\r\n    orthopedist: DoctorSpecialty.ORTHOPEDICS,\r\n    dermatology: DoctorSpecialty.DERMATOLOGY,\r\n    dermatologist: DoctorSpecialty.DERMATOLOGY,\r\n    pediatrics: DoctorSpecialty.PEDIATRICS,\r\n    pediatrician: DoctorSpecialty.PEDIATRICS,\r\n    gynecology: DoctorSpecialty.GYNECOLOGY,\r\n    gynecologist: DoctorSpecialty.GYNECOLOGY,\r\n    psychiatry: DoctorSpecialty.PSYCHIATRY,\r\n    psychiatrist: DoctorSpecialty.PSYCHIATRY,\r\n    radiology: DoctorSpecialty.RADIOLOGY,\r\n    radiologist: DoctorSpecialty.RADIOLOGY,\r\n    pathology: DoctorSpecialty.PATHOLOGY,\r\n    pathologist: DoctorSpecialty.PATHOLOGY,\r\n    oncology: DoctorSpecialty.ONCOLOGY,\r\n    oncologist: DoctorSpecialty.ONCOLOGY,\r\n    endocrinology: DoctorSpecialty.ENDOCRINOLOGY,\r\n    endocrinologist: DoctorSpecialty.ENDOCRINOLOGY,\r\n    gastroenterology: DoctorSpecialty.GASTROENTEROLOGY,\r\n    gastroenterologist: DoctorSpecialty.GASTROENTEROLOGY,\r\n    pulmonology: DoctorSpecialty.PULMONOLOGY,\r\n    pulmonologist: DoctorSpecialty.PULMONOLOGY,\r\n    nephrology: DoctorSpecialty.NEPHROLOGY,\r\n    nephrologist: DoctorSpecialty.NEPHROLOGY,\r\n    ophthalmology: DoctorSpecialty.OPHTHALMOLOGY,\r\n    ophthalmologist: DoctorSpecialty.OPHTHALMOLOGY,\r\n    ent: DoctorSpecialty.ENT,\r\n    \"emergency medicine\": DoctorSpecialty.EMERGENCY_MEDICINE,\r\n    emergency: DoctorSpecialty.EMERGENCY_MEDICINE,\r\n    \"family medicine\": DoctorSpecialty.FAMILY_MEDICINE,\r\n    anesthesiology: DoctorSpecialty.ANESTHESIOLOGY,\r\n  };\r\n\r\n  return mapping[specialist.toLowerCase()] || null;\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAOU;AAPV;AACA;AAEA;AACA;;;;;AAEA,MAAM,KAAK,IAAI,6JAAA,CAAA,cAAW,CAAC;IACzB,MAAM;AACR;AAGO,eAAe,uBACpB,MAAc,EACd,QAAgB,EAChB,YAA0B;IAE1B,MAAM,SAAS,CAAA,GAAA,mIAAA,CAAA,wBAAqB,AAAD,EAAE;IACrC,MAAM,eAAe,QAAQ,kBAAkB;IAE/C,MAAM,SAAS,MAAM,GAAG,MAAM,CAAC,eAAe,CAAC;QAC7C,OAAO;QACP,UAAU;YACR;gBACE,YAAY;oBACV,MAAM;oBACN;gBACF;YACF;YACA;gBACE,MAAM,CAAC;uEACwD,EAAE,aAC9D,WAAW,GACX,OAAO,CAAC,KAAK,KAAK;;AAE7B,EAAE,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;QAkCP,CAAC;YACH;SACD;IACH;IAEA,kCAAkC;IAClC,MAAM,MAAM,OAAO,IAAI,EAAE,UAAU;IACnC,MAAM,YAAY,IAAI,KAAK,CAAC;IAC5B,MAAM,aAAa,WAAW,CAAC,EAAE,IAAI;IAErC,IAAI;QACF,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM,IAAI,MAAM;IAClB;AACF;AAGO,eAAe,oBAAoB,MAAc,EAAE,QAAgB;IACxE,MAAM,WAAW,MAAM,uBACrB,QACA,UACA,yJAAA,CAAA,eAAY,CAAC,KAAK;IAEpB,OAAO,KAAK,SAAS,CAAC;AACxB;AAGO,eAAe,6BACpB,SAKE;IAIF,MAAM,UAAU,MAAM,QAAQ,UAAU,CACtC,UAAU,GAAG,CAAC,OAAO;QACnB,MAAM,WAAW,MAAM,uBACrB,IAAI,MAAM,EACV,IAAI,QAAQ,EACZ,IAAI,YAAY;QAElB,OAAO;YAAE,UAAU,IAAI,QAAQ;YAAE;QAAS;IAC5C;IAGF,OAAO,QAAQ,GAAG,CAAC,CAAC,QAAQ;QAC1B,IAAI,OAAO,MAAM,KAAK,aAAa;YACjC,OAAO,OAAO,KAAK;QACrB,OAAO;YACL,OAAO;gBACL,UAAU,SAAS,CAAC,MAAM,CAAC,QAAQ;gBACnC,UAAU;gBACV,OAAO,OAAO,MAAM,EAAE,WAAW;YACnC;QACF;IACF;AACF;AAGO,SAAS,qBAAqB,QAAgC;IAKnE,MAAM,cAAc,IAAI;IACxB,MAAM,UAAoB,EAAE;IAE5B,cAAc;IACd,MAAM,eAAe,gBAAgB,SAAS,OAAO;IAErD,2DAA2D;IAC3D,SAAS,uBAAuB,CAAC,OAAO,CAAC,CAAC;QACxC,MAAM,YAAY,yBAAyB;QAC3C,IAAI,WAAW;YACb,YAAY,GAAG,CAAC;YAChB,QAAQ,IAAI,CAAC,CAAC,eAAe,EAAE,YAAY;QAC7C;IACF;IAEA,kDAAkD;IAClD,MAAM,gBAAgB;WACjB,SAAS,QAAQ;WACjB,SAAS,qBAAqB;KAClC,CAAC,GAAG,CAAC,CAAC,OAAS,KAAK,WAAW;IAEhC,cAAc,OAAO,CAAC,CAAC;QACrB,OAAO,OAAO,CAAC,0HAAA,CAAA,4BAAyB,EAAE,OAAO,CAC/C,CAAC,CAAC,SAAS,mBAAmB;YAC5B,IAAI,UAAU,QAAQ,CAAC,QAAQ,WAAW,KAAK;gBAC7C,mBAAmB,OAAO,CAAC,CAAC;oBAC1B,YAAY,GAAG,CAAC;oBAChB,QAAQ,IAAI,CAAC,CAAC,WAAW,EAAE,UAAU,WAAW,EAAE,WAAW;gBAC/D;YACF;QACF;IAEJ;IAEA,+CAA+C;IAC/C,IAAI,YAAY,IAAI,KAAK,GAAG;QAC1B,YAAY,GAAG,CAAC,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;QAChD,QAAQ,IAAI,CAAC;IACf;IAEA,OAAO;QACL,wBAAwB,MAAM,IAAI,CAAC;QACnC;QACA,eAAe,QAAQ,IAAI,CAAC;IAC9B;AACF;AAEA,mBAAmB;AACnB,SAAS;IACP,OAAO,CAAC;;;;;;;EAOR,CAAC;AACH;AAEA,SAAS;IACP,OAAO;QACL,eAAe;QACf,SAAS;YACP,MAAM;YACN,KAAK;YACL,QAAQ;QACV;QACA,UAAU,EAAE;QACZ,aAAa,EAAE;QACf,uBAAuB,EAAE;QACzB,iBAAiB,EAAE;QACnB,yBAAyB,EAAE;QAC3B,SAAS;IACX;AACF;AAEA,SAAS,gBAAgB,OAAe;IACtC,OAAQ,QAAQ,WAAW;QACzB,KAAK;QACL,KAAK;YACH,OAAO,yJAAA,CAAA,eAAY,CAAC,IAAI;QAC1B,KAAK;QACL,KAAK;YACH,OAAO,yJAAA,CAAA,eAAY,CAAC,MAAM;QAC5B,KAAK;QACL;YACE,OAAO,yJAAA,CAAA,eAAY,CAAC,GAAG;IAC3B;AACF;AAEA,SAAS,yBAAyB,UAAkB;IAClD,MAAM,UAA2C;QAC/C,oBAAoB,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;QACpD,YAAY,yJAAA,CAAA,kBAAe,CAAC,UAAU;QACtC,cAAc,yJAAA,CAAA,kBAAe,CAAC,UAAU;QACxC,WAAW,yJAAA,CAAA,kBAAe,CAAC,SAAS;QACpC,aAAa,yJAAA,CAAA,kBAAe,CAAC,SAAS;QACtC,aAAa,yJAAA,CAAA,kBAAe,CAAC,WAAW;QACxC,aAAa,yJAAA,CAAA,kBAAe,CAAC,WAAW;QACxC,aAAa,yJAAA,CAAA,kBAAe,CAAC,WAAW;QACxC,eAAe,yJAAA,CAAA,kBAAe,CAAC,WAAW;QAC1C,YAAY,yJAAA,CAAA,kBAAe,CAAC,UAAU;QACtC,cAAc,yJAAA,CAAA,kBAAe,CAAC,UAAU;QACxC,YAAY,yJAAA,CAAA,kBAAe,CAAC,UAAU;QACtC,cAAc,yJAAA,CAAA,kBAAe,CAAC,UAAU;QACxC,YAAY,yJAAA,CAAA,kBAAe,CAAC,UAAU;QACtC,cAAc,yJAAA,CAAA,kBAAe,CAAC,UAAU;QACxC,WAAW,yJAAA,CAAA,kBAAe,CAAC,SAAS;QACpC,aAAa,yJAAA,CAAA,kBAAe,CAAC,SAAS;QACtC,WAAW,yJAAA,CAAA,kBAAe,CAAC,SAAS;QACpC,aAAa,yJAAA,CAAA,kBAAe,CAAC,SAAS;QACtC,UAAU,yJAAA,CAAA,kBAAe,CAAC,QAAQ;QAClC,YAAY,yJAAA,CAAA,kBAAe,CAAC,QAAQ;QACpC,eAAe,yJAAA,CAAA,kBAAe,CAAC,aAAa;QAC5C,iBAAiB,yJAAA,CAAA,kBAAe,CAAC,aAAa;QAC9C,kBAAkB,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;QAClD,oBAAoB,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;QACpD,aAAa,yJAAA,CAAA,kBAAe,CAAC,WAAW;QACxC,eAAe,yJAAA,CAAA,kBAAe,CAAC,WAAW;QAC1C,YAAY,yJAAA,CAAA,kBAAe,CAAC,UAAU;QACtC,cAAc,yJAAA,CAAA,kBAAe,CAAC,UAAU;QACxC,eAAe,yJAAA,CAAA,kBAAe,CAAC,aAAa;QAC5C,iBAAiB,yJAAA,CAAA,kBAAe,CAAC,aAAa;QAC9C,KAAK,yJAAA,CAAA,kBAAe,CAAC,GAAG;QACxB,sBAAsB,yJAAA,CAAA,kBAAe,CAAC,kBAAkB;QACxD,WAAW,yJAAA,CAAA,kBAAe,CAAC,kBAAkB;QAC7C,mBAAmB,yJAAA,CAAA,kBAAe,CAAC,eAAe;QAClD,gBAAgB,yJAAA,CAAA,kBAAe,CAAC,cAAc;IAChD;IAEA,OAAO,OAAO,CAAC,WAAW,WAAW,GAAG,IAAI;AAC9C", "debugId": null}}, {"offset": {"line": 1917, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/app/%28app%29/arthamed/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useState, useCallback } from \"react\";\r\nimport { toast } from \"sonner\";\r\nimport {\r\n  <PERSON>,\r\n  CardContent,\r\n  CardDescription,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"@/components/ui/card\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Shield, Upload, Users, Zap } from \"lucide-react\";\r\n\r\nimport { FileUpload } from \"@/components/arthamed/file-upload\";\r\nimport { UploadedFiles } from \"@/components/arthamed/uploaded-files\";\r\nimport { AnalysisProgress } from \"@/components/arthamed/analysis-progress\";\r\nimport { analyzeMedicalImage } from \"@/lib/gemini\";\r\n\r\ninterface UploadedFile {\r\n  id: string;\r\n  name: string;\r\n  size: number;\r\n  type: string;\r\n  preview?: string;\r\n}\r\n\r\nexport default function HomePage() {\r\n  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);\r\n  const [isUploading, setIsUploading] = useState(false);\r\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\r\n  const [analysisProgress, setAnalysisProgress] = useState(0);\r\n  const [analysisStage, setAnalysisStage] = useState(\"\");\r\n  const [analysisComplete, setAnalysisComplete] = useState(false);\r\n  const [analysisResult, setAnalysisResult] = useState<{\r\n    summary: string;\r\n    keyFindings: string[];\r\n  }>({ summary: \"\", keyFindings: [] });\r\n  const [aiDecision, setAiDecision] = useState<any>(null);\r\n\r\n  const resetAnalysis = () => {\r\n    uploadedFiles.forEach((file) => {\r\n      if (file.preview) URL.revokeObjectURL(file.preview);\r\n    });\r\n    setUploadedFiles([]);\r\n    setAnalysisProgress(0);\r\n    setAnalysisStage(\"\");\r\n    setAnalysisComplete(false);\r\n    setAnalysisResult({ summary: \"\", keyFindings: [] });\r\n    setAiDecision(null);\r\n  };\r\n\r\n  const handleFileUpload = useCallback(async (files: File[]) => {\r\n    setIsUploading(true);\r\n    try {\r\n      const newFiles: UploadedFile[] = await Promise.all(\r\n        files.map(async (file) => {\r\n          const preview = file.type.startsWith(\"image/\")\r\n            ? URL.createObjectURL(file)\r\n            : undefined;\r\n\r\n          return {\r\n            id: crypto.randomUUID(),\r\n            name: file.name,\r\n            size: file.size,\r\n            type: file.type,\r\n            preview,\r\n          };\r\n        })\r\n      );\r\n      setUploadedFiles(newFiles);\r\n      toast.success(\"Files uploaded\", {\r\n        description: `${files.length} file(s) ready for analysis.`,\r\n      });\r\n    } catch {\r\n      toast.error(\"Upload failed\", { description: \"Please try again.\" });\r\n    } finally {\r\n      setIsUploading(false);\r\n    }\r\n  }, []);\r\n\r\n  const handleRemoveFile = useCallback((fileId: string) => {\r\n    setUploadedFiles((prev) => {\r\n      const target = prev.find((f) => f.id === fileId);\r\n      if (target?.preview) URL.revokeObjectURL(target.preview);\r\n      return prev.filter((f) => f.id !== fileId);\r\n    });\r\n  }, []);\r\n\r\n  const handleStartAnalysis = async () => {\r\n    if (!uploadedFiles.length) {\r\n      toast.error(\"No files to analyze\", {\r\n        description: \"Please upload at least one file.\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    setIsAnalyzing(true);\r\n    setAnalysisComplete(false);\r\n    setAnalysisProgress(0);\r\n    setAnalysisStage(\"\");\r\n\r\n    const toastId = toast.loading(\"Starting analysis...\");\r\n    const file = uploadedFiles[0];\r\n\r\n    try {\r\n      setAnalysisStage(\"Reading file...\");\r\n      setAnalysisProgress(20);\r\n\r\n      const fileBlob = await fetch(file.preview!).then((r) => r.blob());\r\n      const buffer = await fileBlob.arrayBuffer();\r\n      const base64 = Buffer.from(buffer).toString(\"base64\");\r\n\r\n      setAnalysisStage(\"Sending your document...\");\r\n      setAnalysisProgress(50);\r\n\r\n      const responseText = await analyzeMedicalImage(base64, file.type);\r\n\r\n      const steps = [\r\n        { stage: \"Parsing results...\", progress: 75 },\r\n        { stage: \"Summarizing findings...\", progress: 90 },\r\n        { stage: \"Finalizing output...\", progress: 100 },\r\n      ];\r\n\r\n      for (const step of steps) {\r\n        setAnalysisStage(step.stage);\r\n        setAnalysisProgress(step.progress);\r\n        await new Promise((res) => setTimeout(res, 600));\r\n      }\r\n\r\n      const jsonMatch = responseText ? responseText.match(/\\{[\\s\\S]+\\}/) : null;\r\n      const result = JSON.parse(jsonMatch?.[0] || \"{}\");\r\n\r\n      const formattedSummary = `🧠 Likely Conditions: ${result.diagnoses_or_keywords.join(\r\n        \", \"\r\n      )}`;\r\n      const formattedFindings = [\r\n        `👤 Patient: ${result.patient.name ?? \"Unknown\"}, Age: ${\r\n          result.patient.age ?? \"?\"\r\n        } (${result.patient.gender})`,\r\n        `📄 Document Type: ${result.document_type}`,\r\n        `🤒 Symptoms: ${result.symptoms.join(\", \")}`,\r\n        `💊 Medications: ${result.medications.join(\", \")}`,\r\n        `🧪 Suggested Tests: ${result.suggested_tests.join(\", \")}`,\r\n        `👨‍⚕️ Specialists: ${result.recommended_specialists.join(\", \")}`,\r\n        `⚠️ Urgency: ${result.urgency}`,\r\n      ];\r\n\r\n      setAnalysisResult({\r\n        summary: formattedSummary,\r\n        keyFindings: formattedFindings,\r\n      });\r\n      setAiDecision(result);\r\n      setAnalysisComplete(true);\r\n\r\n      toast.success(\"Analysis completed\", {\r\n        id: toastId,\r\n        description: \"Medical data extracted successfully.\",\r\n      });\r\n    } catch (err) {\r\n      toast.error(\"API failed\", {\r\n        id: toastId,\r\n        description: \"Please try again later.\",\r\n      });\r\n      console.error(err);\r\n    } finally {\r\n      setIsAnalyzing(false);\r\n    }\r\n  };\r\n\r\n  const renderAnalysisActions = () => {\r\n    if (analysisComplete) return null;\r\n\r\n    return (\r\n      <Button\r\n        onClick={handleStartAnalysis}\r\n        size=\"lg\"\r\n        disabled={isAnalyzing}\r\n        className=\"bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800 text-white px-8\"\r\n      >\r\n        {isAnalyzing ? \"Analyzing...\" : \"Start Analysis\"}\r\n        <Zap className=\"w-4 h-4 ml-2\" />\r\n      </Button>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 dark:from-background dark:via-background dark:to-background\">\r\n      <main className=\"container mx-auto px-4 py-8 flex flex-col items-center\">\r\n        <div className=\"text-center mb-12 w-full max-w-4xl mx-auto\">\r\n          <h2 className=\"text-4xl font-bold text-foreground dark:text-zinc-300 mb-4\">\r\n            <span className=\"text-emerald-600\">Artha Med</span> – Simplify\r\n            Medical Documents with AI\r\n          </h2>\r\n          <p className=\"text-xl text-muted-foreground mb-8 mt-8\">\r\n            Decode your medical paperwork with AI: Upload prescriptions, test\r\n            results, or clinical notes and get explanations in language you can\r\n            actually understand.\r\n          </p>\r\n\r\n          <div className=\"flex flex-wrap justify-center gap-3 mb-8\">\r\n            {[\r\n              {\r\n                icon: <Shield className=\"w-4 h-4 text-white\" />,\r\n                label: \"Medical Jargon Simplifier\",\r\n                bgColor: \"bg-green-600 dark:bg-green-700\",\r\n                hoverColor: \"hover:bg-green-700 dark:hover:bg-green-800\",\r\n                borderColor: \"border-green-600 dark:border-green-700\",\r\n              },\r\n              {\r\n                icon: <Zap className=\"w-4 h-4 text-white\" />,\r\n                label: \"Instant Analysis\",\r\n                bgColor: \"bg-blue-600 dark:bg-blue-700\",\r\n                hoverColor: \"hover:bg-blue-700 dark:hover:bg-blue-800\",\r\n                borderColor: \"border-blue-600 dark:border-blue-700\",\r\n              },\r\n              {\r\n                icon: <Users className=\"w-4 h-4 text-white\" />,\r\n                label: \"Patient-Friendly\",\r\n                bgColor: \"bg-purple-600 dark:bg-purple-700\",\r\n                hoverColor: \"hover:bg-purple-700 dark:hover:bg-purple-800\",\r\n                borderColor: \"border-purple-600 dark:border-purple-700\",\r\n              },\r\n            ].map(({ icon, label, bgColor, hoverColor, borderColor }, i) => (\r\n              <div\r\n                key={i}\r\n                className={`flex items-center space-x-2 ${bgColor} ${hoverColor} ${borderColor} rounded-full px-4 py-2 shadow-sm border transition-colors duration-200`}\r\n              >\r\n                {icon}\r\n                <span className=\"text-sm font-medium text-white\">{label}</span>\r\n              </div>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"w-full max-w-3xl\">\r\n          <Card className=\"shadow-lg border-0 bg-white/80 dark:bg-background/80 backdrop-blur-sm dark:border-gray-800\">\r\n            <CardHeader>\r\n              <CardTitle className=\"flex items-center space-x-2 dark:text-white\">\r\n                <Upload className=\"w-5 h-5 text-blue-600 dark:text-blue-400\" />\r\n                <span>Upload Medical Documents</span>\r\n              </CardTitle>\r\n              <CardDescription className=\"dark:text-gray-300\">\r\n                Drag and drop files or browse. Accepted formats: PDF, JPG, PNG.\r\n              </CardDescription>\r\n            </CardHeader>\r\n            <CardContent>\r\n              {uploadedFiles.length === 0 ? (\r\n                <FileUpload\r\n                  onFileUpload={handleFileUpload}\r\n                  isUploading={isUploading}\r\n                  acceptedTypes={{\r\n                    \"application/pdf\": [\".pdf\"],\r\n                    \"image/jpeg\": [\".jpg\", \".jpeg\"],\r\n                    \"image/png\": [\".png\"],\r\n                  }}\r\n                />\r\n              ) : (\r\n                <div className=\"mt-6\">\r\n                  <UploadedFiles\r\n                    files={uploadedFiles}\r\n                    onRemoveFile={handleRemoveFile}\r\n                  />\r\n                  <div className=\"mt-6 flex justify-center\">\r\n                    {renderAnalysisActions()}\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </CardContent>\r\n          </Card>\r\n        </div>\r\n\r\n        {isAnalyzing && (\r\n          <div className=\"mt-8 w-full max-w-3xl\">\r\n            <AnalysisProgress\r\n              progress={analysisProgress}\r\n              stage={analysisStage}\r\n              fileCount={uploadedFiles.length}\r\n            />\r\n          </div>\r\n        )}\r\n\r\n        {analysisComplete && (\r\n          <div className=\"mt-8 bg-white/80 dark:bg-background shadow-lg rounded-xl p-6 w-full max-w-5xl space-y-4 border dark:border-gray-800\">\r\n            <h3 className=\"text-2xl font-bold text-green-700 dark:text-green-400\">\r\n              Analysis Results\r\n            </h3>\r\n            <p className=\"text-gray-700 dark:text-gray-300\">\r\n              {analysisResult.summary}\r\n            </p>\r\n\r\n            {analysisResult.keyFindings.length > 0 && (\r\n              <div className=\"mt-4\">\r\n                <h4 className=\"text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2\">\r\n                  Key Findings:\r\n                </h4>\r\n                <ul className=\"list-disc list-inside space-y-1 text-gray-700 dark:text-gray-300\">\r\n                  {analysisResult.keyFindings.map((finding, index) => (\r\n                    <li key={index}>{finding}</li>\r\n                  ))}\r\n                </ul>\r\n              </div>\r\n            )}\r\n\r\n            <Button\r\n              onClick={resetAnalysis}\r\n              variant=\"outline\"\r\n              className=\"mt-6 dark:border-gray-800 dark:text-white dark:hover:bg-gray-900\"\r\n            >\r\n              Analyze New Documents\r\n            </Button>\r\n\r\n            {aiDecision && (\r\n              <Button\r\n                className=\"w-full sm:w-auto bg-emerald-600 hover:bg-emerald-700 text-white mt-4\"\r\n                onClick={() => {\r\n                  const route = aiDecision?.suggested_tests.includes(\r\n                    \"MRI Brain\"\r\n                  )\r\n                    ? \"/smritiyan\"\r\n                    : aiDecision?.recommended_specialists.includes(\r\n                        \"Pulmonologist\"\r\n                      )\r\n                    ? \"/book-pulmonologist\"\r\n                    : \"/appointments\";\r\n\r\n                  window.location.href = route;\r\n                }}\r\n              >\r\n                Proceed to Suggested Next Step →\r\n              </Button>\r\n            )}\r\n          </div>\r\n        )}\r\n      </main>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;AA+GqB;;AA7GrB;AACA;AACA;AAOA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;;;AAjBA;;;;;;;;;;AA2Be,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGhD;QAAE,SAAS;QAAI,aAAa,EAAE;IAAC;IAClC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAElD,MAAM,gBAAgB;QACpB,cAAc,OAAO,CAAC,CAAC;YACrB,IAAI,KAAK,OAAO,EAAE,IAAI,eAAe,CAAC,KAAK,OAAO;QACpD;QACA,iBAAiB,EAAE;QACnB,oBAAoB;QACpB,iBAAiB;QACjB,oBAAoB;QACpB,kBAAkB;YAAE,SAAS;YAAI,aAAa,EAAE;QAAC;QACjD,cAAc;IAChB;IAEA,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,OAAO;YAC1C,eAAe;YACf,IAAI;gBACF,MAAM,WAA2B,MAAM,QAAQ,GAAG,CAChD,MAAM,GAAG;8DAAC,OAAO;wBACf,MAAM,UAAU,KAAK,IAAI,CAAC,UAAU,CAAC,YACjC,IAAI,eAAe,CAAC,QACpB;wBAEJ,OAAO;4BACL,IAAI,OAAO,UAAU;4BACrB,MAAM,KAAK,IAAI;4BACf,MAAM,KAAK,IAAI;4BACf,MAAM,KAAK,IAAI;4BACf;wBACF;oBACF;;gBAEF,iBAAiB;gBACjB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB;oBAC9B,aAAa,GAAG,MAAM,MAAM,CAAC,4BAA4B,CAAC;gBAC5D;YACF,EAAE,OAAM;gBACN,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,iBAAiB;oBAAE,aAAa;gBAAoB;YAClE,SAAU;gBACR,eAAe;YACjB;QACF;iDAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE,CAAC;YACpC;0DAAiB,CAAC;oBAChB,MAAM,SAAS,KAAK,IAAI;yEAAC,CAAC,IAAM,EAAE,EAAE,KAAK;;oBACzC,IAAI,QAAQ,SAAS,IAAI,eAAe,CAAC,OAAO,OAAO;oBACvD,OAAO,KAAK,MAAM;kEAAC,CAAC,IAAM,EAAE,EAAE,KAAK;;gBACrC;;QACF;iDAAG,EAAE;IAEL,MAAM,sBAAsB;QAC1B,IAAI,CAAC,cAAc,MAAM,EAAE;YACzB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,uBAAuB;gBACjC,aAAa;YACf;YACA;QACF;QAEA,eAAe;QACf,oBAAoB;QACpB,oBAAoB;QACpB,iBAAiB;QAEjB,MAAM,UAAU,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAC9B,MAAM,OAAO,aAAa,CAAC,EAAE;QAE7B,IAAI;YACF,iBAAiB;YACjB,oBAAoB;YAEpB,MAAM,WAAW,MAAM,MAAM,KAAK,OAAO,EAAG,IAAI,CAAC,CAAC,IAAM,EAAE,IAAI;YAC9D,MAAM,SAAS,MAAM,SAAS,WAAW;YACzC,MAAM,SAAS,8JAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,QAAQ,CAAC;YAE5C,iBAAiB;YACjB,oBAAoB;YAEpB,MAAM,eAAe,MAAM,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,KAAK,IAAI;YAEhE,MAAM,QAAQ;gBACZ;oBAAE,OAAO;oBAAsB,UAAU;gBAAG;gBAC5C;oBAAE,OAAO;oBAA2B,UAAU;gBAAG;gBACjD;oBAAE,OAAO;oBAAwB,UAAU;gBAAI;aAChD;YAED,KAAK,MAAM,QAAQ,MAAO;gBACxB,iBAAiB,KAAK,KAAK;gBAC3B,oBAAoB,KAAK,QAAQ;gBACjC,MAAM,IAAI,QAAQ,CAAC,MAAQ,WAAW,KAAK;YAC7C;YAEA,MAAM,YAAY,eAAe,aAAa,KAAK,CAAC,iBAAiB;YACrE,MAAM,SAAS,KAAK,KAAK,CAAC,WAAW,CAAC,EAAE,IAAI;YAE5C,MAAM,mBAAmB,CAAC,sBAAsB,EAAE,OAAO,qBAAqB,CAAC,IAAI,CACjF,OACC;YACH,MAAM,oBAAoB;gBACxB,CAAC,YAAY,EAAE,OAAO,OAAO,CAAC,IAAI,IAAI,UAAU,OAAO,EACrD,OAAO,OAAO,CAAC,GAAG,IAAI,IACvB,EAAE,EAAE,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;gBAC7B,CAAC,kBAAkB,EAAE,OAAO,aAAa,EAAE;gBAC3C,CAAC,aAAa,EAAE,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO;gBAC5C,CAAC,gBAAgB,EAAE,OAAO,WAAW,CAAC,IAAI,CAAC,OAAO;gBAClD,CAAC,oBAAoB,EAAE,OAAO,eAAe,CAAC,IAAI,CAAC,OAAO;gBAC1D,CAAC,mBAAmB,EAAE,OAAO,uBAAuB,CAAC,IAAI,CAAC,OAAO;gBACjE,CAAC,YAAY,EAAE,OAAO,OAAO,EAAE;aAChC;YAED,kBAAkB;gBAChB,SAAS;gBACT,aAAa;YACf;YACA,cAAc;YACd,oBAAoB;YAEpB,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,sBAAsB;gBAClC,IAAI;gBACJ,aAAa;YACf;QACF,EAAE,OAAO,KAAK;YACZ,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,cAAc;gBACxB,IAAI;gBACJ,aAAa;YACf;YACA,QAAQ,KAAK,CAAC;QAChB,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,kBAAkB,OAAO;QAE7B,qBACE,6LAAC,qIAAA,CAAA,SAAM;YACL,SAAS;YACT,MAAK;YACL,UAAU;YACV,WAAU;;gBAET,cAAc,iBAAiB;8BAChC,6LAAC,mMAAA,CAAA,MAAG;oBAAC,WAAU;;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAK,WAAU;;8BACd,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;oCAAK,WAAU;8CAAmB;;;;;;gCAAgB;;;;;;;sCAGrD,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;sCAMvD,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCACE,oBAAM,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCACxB,OAAO;oCACP,SAAS;oCACT,YAAY;oCACZ,aAAa;gCACf;gCACA;oCACE,oBAAM,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCACrB,OAAO;oCACP,SAAS;oCACT,YAAY;oCACZ,aAAa;gCACf;gCACA;oCACE,oBAAM,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCACvB,OAAO;oCACP,SAAS;oCACT,YAAY;oCACZ,aAAa;gCACf;6BACD,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,EAAE,kBACxD,6LAAC;oCAEC,WAAW,CAAC,4BAA4B,EAAE,QAAQ,CAAC,EAAE,WAAW,CAAC,EAAE,YAAY,uEAAuE,CAAC;;wCAEtJ;sDACD,6LAAC;4CAAK,WAAU;sDAAkC;;;;;;;mCAJ7C;;;;;;;;;;;;;;;;8BAUb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,mIAAA,CAAA,kBAAe;wCAAC,WAAU;kDAAqB;;;;;;;;;;;;0CAIlD,6LAAC,mIAAA,CAAA,cAAW;0CACT,cAAc,MAAM,KAAK,kBACxB,6LAAC,mJAAA,CAAA,aAAU;oCACT,cAAc;oCACd,aAAa;oCACb,eAAe;wCACb,mBAAmB;4CAAC;yCAAO;wCAC3B,cAAc;4CAAC;4CAAQ;yCAAQ;wCAC/B,aAAa;4CAAC;yCAAO;oCACvB;;;;;yDAGF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,sJAAA,CAAA,gBAAa;4CACZ,OAAO;4CACP,cAAc;;;;;;sDAEhB,6LAAC;4CAAI,WAAU;sDACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAQZ,6BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,yJAAA,CAAA,mBAAgB;wBACf,UAAU;wBACV,OAAO;wBACP,WAAW,cAAc,MAAM;;;;;;;;;;;gBAKpC,kCACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,6LAAC;4BAAE,WAAU;sCACV,eAAe,OAAO;;;;;;wBAGxB,eAAe,WAAW,CAAC,MAAM,GAAG,mBACnC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA8D;;;;;;8CAG5E,6LAAC;oCAAG,WAAU;8CACX,eAAe,WAAW,CAAC,GAAG,CAAC,CAAC,SAAS,sBACxC,6LAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAMjB,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,SAAQ;4BACR,WAAU;sCACX;;;;;;wBAIA,4BACC,6LAAC,qIAAA,CAAA,SAAM;4BACL,WAAU;4BACV,SAAS;gCACP,MAAM,QAAQ,YAAY,gBAAgB,SACxC,eAEE,eACA,YAAY,wBAAwB,SAClC,mBAEF,wBACA;gCAEJ,OAAO,QAAQ,CAAC,IAAI,GAAG;4BACzB;sCACD;;;;;;;;;;;;;;;;;;;;;;;AASf;GAtTwB;KAAA", "debugId": null}}]}