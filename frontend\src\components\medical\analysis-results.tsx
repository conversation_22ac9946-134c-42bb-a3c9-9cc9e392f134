"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { 
  Brain, 
  FileText, 
  User, 
  Pill, 
  Activity, 
  TestTube, 
  Stethoscope,
  AlertTriangle,
  Clock,
  CheckCircle2,
  ChevronDown,
  ChevronRight,
  Download,
  Share,
  Eye
} from "lucide-react"
import { AnalysisResult, GeminiAnalysisResponse, PatientInfo } from "@/types/medical-documents"
import { DOCUMENT_TYPE_LABELS } from "@/lib/document-config"

interface AnalysisResultsProps {
  analysisResult: AnalysisResult
  onViewDocument?: () => void
  onDownloadReport?: () => void
  onShareResults?: () => void
  showActions?: boolean
}

export function AnalysisResults({
  analysisResult,
  onViewDocument,
  onDownloadReport,
  onShareResults,
  showActions = true
}: AnalysisResultsProps) {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['overview']))

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections)
    if (newExpanded.has(section)) {
      newExpanded.delete(section)
    } else {
      newExpanded.add(section)
    }
    setExpandedSections(newExpanded)
  }

  const parsedData: GeminiAnalysisResponse | null = analysisResult.extractedData 
    ? JSON.parse(analysisResult.extractedData) 
    : null

  const parsedPatientInfo: PatientInfo | null = analysisResult.patientInfo
    ? JSON.parse(analysisResult.patientInfo)
    : null

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
        return "default"
      case "processing":
        return "secondary"
      case "failed":
        return "destructive"
      default:
        return "outline"
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
        return <CheckCircle2 className="w-4 h-4" />
      case "processing":
        return <Clock className="w-4 h-4 animate-pulse" />
      case "failed":
        return <AlertTriangle className="w-4 h-4" />
      default:
        return <Clock className="w-4 h-4" />
    }
  }

  const getUrgencyColor = (urgency: string) => {
    switch (urgency.toLowerCase()) {
      case "high":
      case "critical":
        return "destructive"
      case "medium":
        return "default"
      case "low":
      default:
        return "secondary"
    }
  }

  const formatConfidence = (confidence?: number) => {
    if (!confidence) return "N/A"
    return `${Math.round(confidence * 100)}%`
  }

  const formatProcessingTime = (time?: number) => {
    if (!time) return "N/A"
    return `${(time / 1000).toFixed(1)}s`
  }

  if (analysisResult.status === "FAILED") {
    return (
      <Card className="border-red-200 dark:border-red-800">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-red-600 dark:text-red-400">
            <AlertTriangle className="w-5 h-5" />
            <span>Analysis Failed</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-600 dark:text-red-400 mb-4">
            {analysisResult.errorMessage || "An error occurred during analysis"}
          </p>
          {showActions && (
            <div className="flex space-x-2">
              {onViewDocument && (
                <Button variant="outline" size="sm" onClick={onViewDocument}>
                  <Eye className="w-4 h-4 mr-2" />
                  View Document
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Brain className="w-5 h-5 text-blue-600" />
              <span>AI Analysis Results</span>
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Badge variant={getStatusColor(analysisResult.status)} className="flex items-center space-x-1">
                {getStatusIcon(analysisResult.status)}
                <span>{analysisResult.status}</span>
              </Badge>
              {showActions && (
                <div className="flex space-x-1">
                  {onViewDocument && (
                    <Button variant="ghost" size="sm" onClick={onViewDocument}>
                      <Eye className="w-4 h-4" />
                    </Button>
                  )}
                  {onDownloadReport && (
                    <Button variant="ghost" size="sm" onClick={onDownloadReport}>
                      <Download className="w-4 h-4" />
                    </Button>
                  )}
                  {onShareResults && (
                    <Button variant="ghost" size="sm" onClick={onShareResults}>
                      <Share className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-500 dark:text-gray-400">Document Type:</span>
              <p className="font-medium">{parsedData?.document_type || "Unknown"}</p>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">Confidence:</span>
              <p className="font-medium">{formatConfidence(analysisResult.confidence)}</p>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">Processing Time:</span>
              <p className="font-medium">{formatProcessingTime(analysisResult.processingTime)}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Analysis Content */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="medical">Medical Data</TabsTrigger>
          <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          <TabsTrigger value="raw">Raw Data</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Patient Information */}
          {parsedPatientInfo && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <User className="w-5 h-5 text-green-600" />
                  <span>Patient Information</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {parsedPatientInfo.name && (
                    <div>
                      <span className="text-sm text-gray-500 dark:text-gray-400">Name:</span>
                      <p className="font-medium">{parsedPatientInfo.name}</p>
                    </div>
                  )}
                  {parsedPatientInfo.age && (
                    <div>
                      <span className="text-sm text-gray-500 dark:text-gray-400">Age:</span>
                      <p className="font-medium">{parsedPatientInfo.age} years</p>
                    </div>
                  )}
                  {parsedPatientInfo.gender && (
                    <div>
                      <span className="text-sm text-gray-500 dark:text-gray-400">Gender:</span>
                      <p className="font-medium">{parsedPatientInfo.gender}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Urgency Level */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <AlertTriangle className="w-5 h-5 text-orange-600" />
                <span>Urgency Assessment</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4">
                <Badge variant={getUrgencyColor(analysisResult.urgencyLevel)} className="text-lg px-4 py-2">
                  {analysisResult.urgencyLevel} Priority
                </Badge>
                {analysisResult.routingReason && (
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {analysisResult.routingReason}
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="medical" className="space-y-4">
          {/* Symptoms */}
          {analysisResult.symptoms.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Activity className="w-5 h-5 text-red-600" />
                  <span>Symptoms ({analysisResult.symptoms.length})</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {analysisResult.symptoms.map((symptom, index) => (
                    <Badge key={index} variant="outline">
                      {symptom}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Medications */}
          {analysisResult.medications.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Pill className="w-5 h-5 text-blue-600" />
                  <span>Medications ({analysisResult.medications.length})</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {analysisResult.medications.map((medication, index) => (
                    <div key={index} className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <p className="text-sm font-medium">{medication}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Diagnoses */}
          {analysisResult.diagnoses.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="w-5 h-5 text-purple-600" />
                  <span>Diagnoses & Conditions ({analysisResult.diagnoses.length})</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {analysisResult.diagnoses.map((diagnosis, index) => (
                    <Badge key={index} variant="secondary">
                      {diagnosis}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          {/* Suggested Tests */}
          {analysisResult.suggestedTests.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TestTube className="w-5 h-5 text-green-600" />
                  <span>Suggested Tests ({analysisResult.suggestedTests.length})</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {analysisResult.suggestedTests.map((test, index) => (
                    <div key={index} className="flex items-center space-x-2 p-2 border rounded-lg">
                      <TestTube className="w-4 h-4 text-green-600" />
                      <span className="text-sm">{test}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Recommended Specialists */}
          {analysisResult.recommendedSpecialists.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Stethoscope className="w-5 h-5 text-blue-600" />
                  <span>Recommended Specialists ({analysisResult.recommendedSpecialists.length})</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {analysisResult.recommendedSpecialists.map((specialist, index) => (
                    <div key={index} className="flex items-center space-x-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                      <Stethoscope className="w-4 h-4 text-blue-600" />
                      <span className="text-sm font-medium">{specialist}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="raw" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Raw Analysis Data</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="text-xs bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-auto max-h-96">
                {JSON.stringify(parsedData, null, 2)}
              </pre>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
