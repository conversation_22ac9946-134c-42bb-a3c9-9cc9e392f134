"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { 
  Upload, 
  Brain, 
  Stethoscope, 
  Calendar, 
  CheckCircle2,
  ArrowRight,
  FileText,
  Clock,
  AlertTriangle
} from "lucide-react";
import { DocumentUpload } from "@/components/medical/document-upload";
import { AnalysisResults } from "@/components/medical/analysis-results";
import { SmartRouting } from "@/components/medical/smart-routing";
import { EnhancedAppointmentBooking } from "@/components/medical/enhanced-appointment-booking";
import { 
  UploadedFile, 
  DocumentUploadProgress, 
  AnalysisResult,
  RoutingRecommendation,
  MedicalContext
} from "@/types/medical-documents";
import { EnhancedDoctor } from "@/types/doctors";
import { toast } from "sonner";

type FlowStep = "upload" | "analysis" | "routing" | "booking" | "complete";

interface PatientFlowState {
  currentStep: FlowStep;
  uploadedFiles: UploadedFile[];
  uploadProgress: DocumentUploadProgress[];
  analysisResults: AnalysisResult[];
  routingRecommendations: RoutingRecommendation[];
  medicalContext?: MedicalContext;
  selectedDoctorId?: string;
  appointmentId?: string;
}

export default function PatientFlowPage() {
  const [flowState, setFlowState] = useState<PatientFlowState>({
    currentStep: "upload",
    uploadedFiles: [],
    uploadProgress: [],
    analysisResults: [],
    routingRecommendations: [],
  });

  const [isUploading, setIsUploading] = useState(false);
  const [doctors, setDoctors] = useState<EnhancedDoctor[]>([]);

  // Mock data - replace with actual API calls
  useEffect(() => {
    // Load doctors
    setDoctors([
      {
        id: "1",
        name: "Sarah Johnson",
        email: "<EMAIL>",
        specialty: "CARDIOLOGY" as any,
        yearsOfExperience: 15,
        consultationFee: 200,
        isAvailable: true,
      },
      {
        id: "2", 
        name: "Michael Chen",
        email: "<EMAIL>",
        specialty: "NEUROLOGY" as any,
        yearsOfExperience: 12,
        consultationFee: 250,
        isAvailable: true,
      },
    ]);
  }, []);

  const handleFileUpload = async (files: File[], documentType: any) => {
    setIsUploading(true);
    
    try {
      // Convert files to UploadedFile format
      const uploadedFiles: UploadedFile[] = files.map(file => ({
        id: Math.random().toString(36).substring(7),
        name: file.name,
        size: file.size,
        type: file.type,
        preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined,
        file,
      }));

      setFlowState(prev => ({
        ...prev,
        uploadedFiles: [...prev.uploadedFiles, ...uploadedFiles],
      }));

      // Simulate upload progress
      const progressItems: DocumentUploadProgress[] = uploadedFiles.map(file => ({
        fileId: file.id,
        fileName: file.name,
        progress: 0,
        status: "uploading",
      }));

      setFlowState(prev => ({
        ...prev,
        uploadProgress: [...prev.uploadProgress, ...progressItems],
      }));

      // Simulate upload and analysis
      for (const item of progressItems) {
        await simulateProgress(item);
      }

      // Move to analysis step
      setFlowState(prev => ({
        ...prev,
        currentStep: "analysis",
      }));

      toast.success("Documents uploaded and analyzed successfully!");
    } catch (error) {
      toast.error("Failed to upload documents");
    } finally {
      setIsUploading(false);
    }
  };

  const simulateProgress = async (item: DocumentUploadProgress) => {
    // Simulate upload progress
    for (let i = 0; i <= 100; i += 10) {
      await new Promise(resolve => setTimeout(resolve, 100));
      setFlowState(prev => ({
        ...prev,
        uploadProgress: prev.uploadProgress.map(p => 
          p.fileId === item.fileId 
            ? { ...p, progress: i, status: i < 50 ? "uploading" : i < 100 ? "analyzing" : "completed" }
            : p
        ),
      }));
    }

    // Add mock analysis result
    const mockAnalysis: AnalysisResult = {
      id: Math.random().toString(36).substring(7),
      documentId: item.fileId,
      status: "COMPLETED" as any,
      symptoms: ["headache", "nausea", "dizziness"],
      medications: ["ibuprofen 400mg", "ondansetron 4mg"],
      diagnoses: ["migraine", "tension headache"],
      suggestedTests: ["MRI Brain", "Blood pressure check"],
      recommendedSpecialists: ["Neurologist", "General Medicine"],
      urgencyLevel: "MEDIUM" as any,
      confidence: 0.85,
      processingTime: 2500,
      suggestedDoctorIds: ["1", "2"],
      routingReason: "Neurological symptoms suggest specialist consultation",
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    setFlowState(prev => ({
      ...prev,
      analysisResults: [...prev.analysisResults, mockAnalysis],
    }));
  };

  const handleFileRemove = (fileId: string) => {
    setFlowState(prev => ({
      ...prev,
      uploadedFiles: prev.uploadedFiles.filter(f => f.id !== fileId),
      uploadProgress: prev.uploadProgress.filter(p => p.fileId !== fileId),
      analysisResults: prev.analysisResults.filter(a => a.documentId !== fileId),
    }));
  };

  const handleProceedToRouting = () => {
    // Generate medical context from analysis results
    const medicalContext: MedicalContext = {
      documentIds: flowState.analysisResults.map(a => a.documentId),
      symptoms: [...new Set(flowState.analysisResults.flatMap(a => a.symptoms))],
      medications: [...new Set(flowState.analysisResults.flatMap(a => a.medications))],
      diagnoses: [...new Set(flowState.analysisResults.flatMap(a => a.diagnoses))],
      urgencyLevel: flowState.analysisResults.some(a => a.urgencyLevel === "HIGH") ? "HIGH" as any : "MEDIUM" as any,
      analysisDate: new Date(),
      keyFindings: ["Neurological symptoms present", "Medication history documented"],
      recommendedActions: ["Consult neurologist", "Monitor symptoms", "Follow up in 1 week"],
    };

    // Generate routing recommendations
    const recommendations: RoutingRecommendation[] = doctors
      .filter(d => flowState.analysisResults.some(a => a.suggestedDoctorIds.includes(d.id)))
      .map(doctor => ({
        doctorId: doctor.id,
        doctor,
        matchScore: 0.8 + Math.random() * 0.2,
        reason: `Specializes in ${doctor.specialty?.toLowerCase().replace('_', ' ')} relevant to your symptoms`,
        urgencyLevel: medicalContext.urgencyLevel,
        availableSlots: [new Date(Date.now() + ********), new Date(Date.now() + *********)],
      }));

    setFlowState(prev => ({
      ...prev,
      currentStep: "routing",
      medicalContext,
      routingRecommendations: recommendations,
    }));
  };

  const handleBookAppointment = (doctorId: string, context: MedicalContext) => {
    setFlowState(prev => ({
      ...prev,
      currentStep: "booking",
      selectedDoctorId: doctorId,
      medicalContext: context,
    }));
  };

  const handleAppointmentBooked = (appointmentId: string) => {
    setFlowState(prev => ({
      ...prev,
      currentStep: "complete",
      appointmentId,
    }));
  };

  const getStepProgress = () => {
    const steps = ["upload", "analysis", "routing", "booking", "complete"];
    const currentIndex = steps.indexOf(flowState.currentStep);
    return ((currentIndex + 1) / steps.length) * 100;
  };

  const getStepStatus = (step: FlowStep) => {
    const steps = ["upload", "analysis", "routing", "booking", "complete"];
    const currentIndex = steps.indexOf(flowState.currentStep);
    const stepIndex = steps.indexOf(step);
    
    if (stepIndex < currentIndex) return "completed";
    if (stepIndex === currentIndex) return "current";
    return "pending";
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 dark:from-background dark:via-background dark:to-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Smart Patient Flow
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 mb-6">
            Upload → Analyze → Route → Book - All in one seamless experience
          </p>
        </div>

        {/* Progress Bar */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <span className="text-sm font-medium">Overall Progress</span>
              <span className="text-sm text-gray-500">{Math.round(getStepProgress())}%</span>
            </div>
            <Progress value={getStepProgress()} className="h-2 mb-4" />
            
            <div className="flex items-center justify-between">
              {[
                { step: "upload" as FlowStep, icon: Upload, label: "Upload" },
                { step: "analysis" as FlowStep, icon: Brain, label: "Analysis" },
                { step: "routing" as FlowStep, icon: Stethoscope, label: "Routing" },
                { step: "booking" as FlowStep, icon: Calendar, label: "Booking" },
                { step: "complete" as FlowStep, icon: CheckCircle2, label: "Complete" },
              ].map(({ step, icon: Icon, label }) => {
                const status = getStepStatus(step);
                return (
                  <div key={step} className="flex flex-col items-center space-y-2">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                      status === "completed" ? "bg-green-600 text-white" :
                      status === "current" ? "bg-blue-600 text-white" :
                      "bg-gray-200 text-gray-500"
                    }`}>
                      <Icon className="w-5 h-5" />
                    </div>
                    <span className={`text-xs font-medium ${
                      status === "completed" ? "text-green-600" :
                      status === "current" ? "text-blue-600" :
                      "text-gray-500"
                    }`}>
                      {label}
                    </span>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Step Content */}
        <div className="max-w-4xl mx-auto">
          {flowState.currentStep === "upload" && (
            <div className="space-y-6">
              <DocumentUpload
                onFileUpload={handleFileUpload}
                onFileRemove={handleFileRemove}
                uploadProgress={flowState.uploadProgress}
                uploadedFiles={flowState.uploadedFiles}
                isUploading={isUploading}
              />
              
              {flowState.uploadedFiles.length > 0 && !isUploading && (
                <div className="text-center">
                  <Button 
                    onClick={() => setFlowState(prev => ({ ...prev, currentStep: "analysis" }))}
                    size="lg"
                    className="px-8"
                  >
                    Continue to Analysis
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              )}
            </div>
          )}

          {flowState.currentStep === "analysis" && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold mb-2">Analysis Results</h2>
                <p className="text-gray-600 dark:text-gray-400">
                  AI has analyzed your medical documents
                </p>
              </div>
              
              {flowState.analysisResults.map((result) => (
                <AnalysisResults
                  key={result.id}
                  analysisResult={result}
                  showActions={false}
                />
              ))}
              
              {flowState.analysisResults.length > 0 && (
                <div className="text-center">
                  <Button 
                    onClick={handleProceedToRouting}
                    size="lg"
                    className="px-8"
                  >
                    Get Doctor Recommendations
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              )}
            </div>
          )}

          {flowState.currentStep === "routing" && flowState.medicalContext && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold mb-2">Smart Recommendations</h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Based on your medical analysis, here are the best specialists for you
                </p>
              </div>
              
              <SmartRouting
                recommendations={flowState.routingRecommendations}
                medicalContext={flowState.medicalContext}
                onBookAppointment={handleBookAppointment}
              />
            </div>
          )}

          {flowState.currentStep === "booking" && flowState.medicalContext && (
            <div className="space-y-6">
              <div className="text-center mb-6">
                <h2 className="text-2xl font-bold mb-2">Book Your Appointment</h2>
                <p className="text-gray-600 dark:text-gray-400">
                  Complete your booking with pre-filled medical context
                </p>
              </div>
              
              <EnhancedAppointmentBooking
                patientId="current-user-id" // Replace with actual user ID
                doctors={doctors}
                medicalContext={flowState.medicalContext}
                preSelectedDoctorId={flowState.selectedDoctorId}
                onAppointmentBooked={handleAppointmentBooked}
              />
            </div>
          )}

          {flowState.currentStep === "complete" && (
            <div className="text-center space-y-6">
              <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <CheckCircle2 className="w-10 h-10 text-green-600" />
              </div>
              
              <div>
                <h2 className="text-3xl font-bold text-green-600 mb-2">
                  Appointment Booked Successfully!
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Your appointment has been scheduled and the doctor has been provided with your medical context.
                </p>
                
                <div className="space-y-4">
                  <Button size="lg" className="px-8">
                    View Appointment Details
                  </Button>
                  <div>
                    <Button variant="outline" onClick={() => window.location.reload()}>
                      Start New Flow
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
