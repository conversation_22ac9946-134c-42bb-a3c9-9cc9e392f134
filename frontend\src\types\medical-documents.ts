import { DocumentType, AnalysisStatus, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@prisma/client";

// Base medical document interface
export interface MedicalDocument {
  id: string;
  fileName: string;
  originalName: string;
  fileSize: number;
  mimeType: string;
  documentType: DocumentType;
  filePath: string;
  uploadedById: string;
  checksum?: string;
  isProcessed: boolean;
  createdAt: Date;
  updatedAt: Date;
  uploadedBy: {
    id: string;
    name: string | null;
    email: string;
  };
  analysis?: AnalysisResult;
}

// Analysis result interface
export interface AnalysisResult {
  id: string;
  documentId: string;
  status: AnalysisStatus;
  extractedData?: string;
  patientInfo?: string;
  symptoms: string[];
  medications: string[];
  diagnoses: string[];
  suggestedTests: string[];
  recommendedSpecialists: string[];
  urgencyLevel: UrgencyLevel;
  confidence?: number;
  processingTime?: number;
  errorMessage?: string;
  suggestedDoctorIds: string[];
  routingReason?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Parsed patient information from analysis
export interface PatientInfo {
  name?: string;
  age?: number;
  gender?: "Male" | "Female" | "Other";
  contactInfo?: {
    phone?: string;
    email?: string;
  };
}

// Structured analysis data from Gemini
export interface GeminiAnalysisResponse {
  document_type: "Prescription" | "Lab Report" | "MRI Report" | "Discharge Summary" | "Other";
  patient: {
    name?: string;
    age?: number;
    gender?: "Male" | "Female" | "Other";
  };
  symptoms: string[];
  medications: string[];
  diagnoses_or_keywords: string[];
  suggested_tests: string[];
  recommended_specialists: string[];
  urgency: "Low" | "Medium" | "High";
}

// File upload interface
export interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  preview?: string;
  file?: File;
}

// Document upload progress
export interface DocumentUploadProgress {
  fileId: string;
  fileName: string;
  progress: number;
  status: "uploading" | "processing" | "analyzing" | "completed" | "error";
  error?: string;
}

// Smart routing recommendation
export interface RoutingRecommendation {
  doctorId: string;
  doctor: {
    id: string;
    name: string | null;
    email: string;
    specialty?: DoctorSpecialty;
    yearsOfExperience?: number;
    consultationFee?: number;
  };
  matchScore: number;
  reason: string;
  urgencyLevel: UrgencyLevel;
  availableSlots?: Date[];
}

// Medical context for appointments
export interface MedicalContext {
  documentIds: string[];
  symptoms: string[];
  medications: string[];
  diagnoses: string[];
  urgencyLevel: UrgencyLevel;
  analysisDate: Date;
  keyFindings: string[];
  recommendedActions: string[];
}

// Document analysis request
export interface DocumentAnalysisRequest {
  documentId: string;
  base64Data: string;
  mimeType: string;
  documentType: DocumentType;
}

// Batch analysis request
export interface BatchAnalysisRequest {
  documents: DocumentAnalysisRequest[];
  patientId: string;
  priority?: "normal" | "high" | "urgent";
}

// Analysis progress tracking
export interface AnalysisProgress {
  documentId: string;
  fileName: string;
  progress: number;
  stage: string;
  estimatedTimeRemaining?: number;
  error?: string;
}

// Document filter options
export interface DocumentFilter {
  documentType?: DocumentType[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  analysisStatus?: AnalysisStatus[];
  urgencyLevel?: UrgencyLevel[];
  hasAnalysis?: boolean;
}

// Document search options
export interface DocumentSearchOptions {
  query?: string;
  filter?: DocumentFilter;
  sortBy?: "createdAt" | "updatedAt" | "fileName" | "urgencyLevel";
  sortOrder?: "asc" | "desc";
  limit?: number;
  offset?: number;
}

// Enhanced doctor interface with specialties
export interface EnhancedDoctor {
  id: string;
  name: string | null;
  email: string;
  specialty?: DoctorSpecialty;
  licenseNumber?: string;
  yearsOfExperience?: number;
  bio?: string;
  consultationFee?: number;
  rating?: number;
  availableSlots?: Date[];
  isAvailable?: boolean;
}

// Appointment with medical context
export interface AppointmentWithContext {
  id: string;
  patientId: string;
  doctorId: string;
  scheduledAt: Date;
  status: string;
  notes?: string;
  medicalContext?: string;
  urgencyLevel: UrgencyLevel;
  relatedDocumentIds: string[];
  createdAt: Date;
  updatedAt: Date;
  doctor: EnhancedDoctor;
  relatedDocuments?: MedicalDocument[];
  parsedMedicalContext?: MedicalContext;
}

// Document type configuration
export interface DocumentTypeConfig {
  type: DocumentType;
  label: string;
  description: string;
  acceptedMimeTypes: string[];
  maxSize: number;
  icon: string;
  analysisPrompt?: string;
}

// System configuration for document handling
export interface DocumentSystemConfig {
  maxFileSize: number;
  maxFilesPerUpload: number;
  supportedTypes: DocumentTypeConfig[];
  storageProvider: "local" | "s3" | "cloudinary";
  analysisProvider: "gemini" | "openai" | "custom";
  autoAnalysis: boolean;
  retentionPeriod: number; // days
}
