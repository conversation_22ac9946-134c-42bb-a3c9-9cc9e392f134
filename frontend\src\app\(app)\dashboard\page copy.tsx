import { getServerSession } from "@/lib/session";
import React from "react";
import SignOut from "@/components/auth/sign-out";

export default async function DashboardPage() {
  const userSession = await getServerSession();
  return (
    <div className="flex flex-col items-center justify-center h-screen">
      <h1 className="text-2xl font-bold mb-4">Dashboard</h1>
      {userSession ? (
        <p>Welcome, {userSession.user?.name || "User"}!</p>
      ) : (
        <p>Please sign in to access your dashboard.</p>
      )}
      <h2 className="text-lg font-semibold mt-6">User Session Data:</h2>
      <div className="flex items-center justify-center px-4 space-x-4">
        <pre className="mt-4 bg-gray-100 p-4 rounded-lg max-w-xl overflow-x-auto">
          <code className="text-xs">
            {JSON.stringify(userSession?.session, null, 2)}
          </code>
        </pre>
        <pre className="mt-4 bg-gray-100 p-4 rounded-lg max-w-xl overflow-x-auto">
          <code className="text-xs">
            {JSON.stringify(userSession?.user, null, 2)}
          </code>
        </pre>
      </div>
      <SignOut />
    </div>
  );
}
