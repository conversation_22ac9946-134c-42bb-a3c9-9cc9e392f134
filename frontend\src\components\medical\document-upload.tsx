"use client";

import { use<PERSON><PERSON>back, useState } from "react";
import { useDropzone } from "react-dropzone";
import {
  Upload,
  FileText,
  ImageIcon,
  AlertCircle,
  X,
  Eye,
  Brain,
  CheckCircle2,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DocumentType } from "@prisma/client";
import {
  UploadedFile,
  DocumentUploadProgress,
  DocumentTypeConfig,
} from "@/types/medical-documents";
import {
  ACCEPTED_FILE_TYPES,
  DOCUMENT_TYPE_CONFIGS,
  DOCUMENT_SYSTEM_CONFIG,
  formatFileSize,
  validateFileType,
  validateFileSize,
  getFileTypeIcon,
  DOCUMENT_TYPE_LABELS,
} from "@/lib/document-config";

interface DocumentUploadProps {
  onFileUpload: (files: File[], documentType: DocumentType) => void;
  onFileRemove: (fileId: string) => void;
  uploadProgress: DocumentUploadProgress[];
  uploadedFiles: UploadedFile[];
  isUploading: boolean;
  maxFiles?: number;
  autoAnalysis?: boolean;
}

export function DocumentUpload({
  onFileUpload,
  onFileRemove,
  uploadProgress,
  uploadedFiles,
  isUploading,
  maxFiles = DOCUMENT_SYSTEM_CONFIG.maxFilesPerUpload,
  autoAnalysis = true,
}: DocumentUploadProps) {
  const [selectedDocumentType, setSelectedDocumentType] =
    useState<DocumentType>(DocumentType.PRESCRIPTION);
  const [draggedFiles, setDraggedFiles] = useState<File[]>([]);

  const selectedConfig = DOCUMENT_TYPE_CONFIGS.find(
    (config) => config.type === selectedDocumentType
  );

  const onDrop = useCallback(
    (acceptedFiles: File[], rejectedFiles: any[]) => {
      if (rejectedFiles.length > 0) {
        console.log("Rejected files:", rejectedFiles);
        // TODO: Show error toast for rejected files
      }

      if (acceptedFiles.length > 0) {
        // Validate files
        const validFiles = acceptedFiles.filter((file) => {
          const isValidType = validateFileType(file);
          const isValidSize = validateFileSize(file, selectedConfig?.maxSize);
          return isValidType && isValidSize;
        });

        if (validFiles.length > 0) {
          onFileUpload(validFiles, selectedDocumentType);
        }
      }
      setDraggedFiles([]);
    },
    [onFileUpload, selectedDocumentType, selectedConfig?.maxSize]
  );

  const { getRootProps, getInputProps, isDragActive, isDragReject } =
    useDropzone({
      onDrop,
      accept: ACCEPTED_FILE_TYPES,
      maxSize: selectedConfig?.maxSize || DOCUMENT_SYSTEM_CONFIG.maxFileSize,
      maxFiles: maxFiles - uploadedFiles.length,
      disabled: isUploading || uploadedFiles.length >= maxFiles,
      onDragEnter: (event) => {
        const files = Array.from(event.dataTransfer?.files || []);
        setDraggedFiles(files);
      },
      onDragLeave: () => {
        setDraggedFiles([]);
      },
    });

  const getIcon = () => {
    if (isDragActive && !isDragReject) {
      return (
        <Upload className="w-12 h-12 text-blue-600 dark:text-blue-400 animate-bounce" />
      );
    }
    if (isDragReject) {
      return (
        <AlertCircle className="w-12 h-12 text-red-500 dark:text-red-400" />
      );
    }
    return <Upload className="w-12 h-12 text-gray-400 dark:text-gray-500" />;
  };

  const getMessage = () => {
    if (isDragReject) {
      return "File type not supported or file too large";
    }
    if (isDragActive) {
      return `Drop your ${DOCUMENT_TYPE_LABELS[
        selectedDocumentType
      ].toLowerCase()} here...`;
    }
    return `Drag & drop your ${DOCUMENT_TYPE_LABELS[
      selectedDocumentType
    ].toLowerCase()} here`;
  };

  const getFileIcon = (type: string) => {
    const iconName = getFileTypeIcon(type);
    if (iconName === "image") {
      return <ImageIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />;
    }
    return <FileText className="w-5 h-5 text-red-600 dark:text-red-400" />;
  };

  const getFileTypeLabel = (type: string) => {
    if (type === "application/pdf") return "PDF";
    if (type.startsWith("image/")) return type.split("/")[1].toUpperCase();
    return "Unknown";
  };

  const getProgressColor = (progress: number) => {
    if (progress < 25) return "bg-blue-600 dark:bg-blue-500";
    if (progress < 50) return "bg-purple-600 dark:bg-purple-500";
    if (progress < 75) return "bg-orange-600 dark:bg-orange-500";
    return "bg-green-600 dark:bg-green-500";
  };

  const getStatusIcon = (status: DocumentUploadProgress["status"]) => {
    switch (status) {
      case "uploading":
        return <Upload className="w-4 h-4 text-blue-600 animate-pulse" />;
      case "processing":
        return <FileText className="w-4 h-4 text-purple-600 animate-pulse" />;
      case "analyzing":
        return <Brain className="w-4 h-4 text-orange-600 animate-pulse" />;
      case "completed":
        return <CheckCircle2 className="w-4 h-4 text-green-600" />;
      case "error":
        return <AlertCircle className="w-4 h-4 text-red-600" />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Document Type Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Document Type</CardTitle>
        </CardHeader>
        <CardContent>
          <Select
            value={selectedDocumentType}
            onValueChange={(value) =>
              setSelectedDocumentType(value as DocumentType)
            }
            disabled={isUploading}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select document type" />
            </SelectTrigger>
            <SelectContent>
              {DOCUMENT_TYPE_CONFIGS.map((config) => (
                <SelectItem key={config.type} value={config.type}>
                  <div className="flex items-center space-x-2">
                    <span>{config.label}</span>
                    <span className="text-xs text-muted-foreground">
                      - {config.description}
                    </span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {selectedConfig && (
            <div className="mt-3 p-3 bg-muted rounded-lg">
              <p className="text-sm text-muted-foreground">
                {selectedConfig.description}
              </p>
              <div className="flex items-center space-x-4 mt-2 text-xs text-muted-foreground">
                <span>Max size: {formatFileSize(selectedConfig.maxSize)}</span>
                <span>Formats: PDF, JPG, PNG, WebP</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upload Area */}
      <Card>
        <CardContent className="p-6">
          <div
            {...getRootProps()}
            className={cn(
              "border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200",
              isDragActive &&
                !isDragReject &&
                "border-blue-500 bg-blue-50 dark:bg-blue-900/30",
              isDragReject && "border-red-500 bg-red-50 dark:bg-red-900/20",
              !isDragActive &&
                "border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 hover:bg-gray-50 dark:hover:bg-gray-700/50",
              (isUploading || uploadedFiles.length >= maxFiles) &&
                "pointer-events-none opacity-70"
            )}
          >
            <input {...getInputProps()} />

            <div className="flex flex-col items-center space-y-4">
              {getIcon()}

              <div>
                <p className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {getMessage()}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                  or click to browse your files
                </p>

                <Button
                  variant="outline"
                  disabled={isUploading || uploadedFiles.length >= maxFiles}
                  className="dark:border-gray-600 dark:text-white dark:hover:bg-gray-700"
                >
                  Choose Files
                </Button>
              </div>

              <div className="flex flex-wrap justify-center gap-2 text-xs text-gray-500 dark:text-gray-400">
                <span className="flex items-center space-x-1">
                  <FileText className="w-3 h-3" />
                  <span>PDF</span>
                </span>
                <span className="flex items-center space-x-1">
                  <ImageIcon className="w-3 h-3" />
                  <span>JPG</span>
                </span>
                <span className="flex items-center space-x-1">
                  <ImageIcon className="w-3 h-3" />
                  <span>PNG</span>
                </span>
                <span>
                  • Max{" "}
                  {formatFileSize(
                    selectedConfig?.maxSize ||
                      DOCUMENT_SYSTEM_CONFIG.maxFileSize
                  )}{" "}
                  per file
                </span>
                <span>• {maxFiles - uploadedFiles.length} files remaining</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Upload Progress */}
      {uploadProgress.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center space-x-2">
              <Brain className="w-5 h-5 text-blue-600" />
              <span>Processing Documents</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {uploadProgress.map((progress) => (
              <div key={progress.fileId} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(progress.status)}
                    <span className="text-sm font-medium truncate">
                      {progress.fileName}
                    </span>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    {progress.progress}%
                  </span>
                </div>

                <div className="relative">
                  <Progress
                    value={progress.progress}
                    className="h-2 bg-gray-200 dark:bg-gray-700"
                  />
                  <div
                    className={`absolute top-0 left-0 h-2 rounded-full transition-all duration-500 ${getProgressColor(
                      progress.progress
                    )}`}
                    style={{ width: `${progress.progress}%` }}
                  />
                </div>

                {progress.error && (
                  <p className="text-xs text-red-600 dark:text-red-400">
                    Error: {progress.error}
                  </p>
                )}
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Uploaded Files */}
      {uploadedFiles.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">
              Uploaded Documents ({uploadedFiles.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {uploadedFiles.map((file) => (
              <div
                key={file.id}
                className="flex items-center justify-between p-3 border rounded-lg"
              >
                <div className="flex items-center space-x-3 flex-1 min-w-0">
                  {file.preview ? (
                    <div className="relative w-12 h-12 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800 flex-shrink-0">
                      <img
                        src={file.preview}
                        alt={file.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ) : (
                    <div className="w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-800 flex items-center justify-center flex-shrink-0">
                      {getFileIcon(file.type)}
                    </div>
                  )}

                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {file.name}
                    </p>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge variant="secondary" className="text-xs">
                        {getFileTypeLabel(file.type)}
                      </Badge>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {formatFileSize(file.size)}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-2 flex-shrink-0">
                  {file.preview && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => window.open(file.preview, "_blank")}
                      className="dark:hover:bg-gray-700"
                    >
                      <Eye className="w-4 h-4 text-gray-700 dark:text-gray-300" />
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onFileRemove(file.id)}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/30"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
