{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_a3ee78ae._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_d2eff509.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|api\\/auth|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|api/auth|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "aUqAtsCCdWmJ30GchUL4G95uoOqTsWTZjYd6YQ/FMRs=", "__NEXT_PREVIEW_MODE_ID": "40e5fba6eb31b415d9ff9cac40c46a1d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "a8bd9669854618f0d9e8b38193fc625488df44d3ed3879e9d9ecddde130ea906", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "754ccfc2bb79c31baa12319081914aa19577c55398756d4905062536b0bb6757"}}}, "sortedMiddleware": ["/"], "functions": {}}