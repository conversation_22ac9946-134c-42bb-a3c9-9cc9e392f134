"use server";

import prisma from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { writeFile, mkdir } from "fs/promises";
import { join } from "path";
import { randomUUID } from "crypto";
import { createHash } from "crypto";
import { 
  analyzeMedicalDocument, 
  generateSmartRouting,
  batchAnalyzeMedicalDocuments 
} from "@/lib/gemini";
import { 
  MedicalDocument, 
  AnalysisResult,
  DocumentUploadProgress,
  BatchAnalysisRequest 
} from "@/types/medical-documents";

// Upload and store medical document
export async function uploadMedicalDocument(
  formData: FormData,
  userId: string,
  documentType: string
): Promise<{ success: boolean; documentId?: string; error?: string }> {
  try {
    const file = formData.get("file") as File;
    if (!file) {
      return { success: false, error: "No file provided" };
    }

    // Validate file
    const maxSize = 20 * 1024 * 1024; // 20MB
    if (file.size > maxSize) {
      return { success: false, error: "File too large" };
    }

    // Generate unique filename and calculate checksum
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    const checksum = createHash("sha256").update(buffer).digest("hex");
    const fileExtension = file.name.split(".").pop();
    const uniqueFileName = `${randomUUID()}.${fileExtension}`;

    // Check for duplicate
    const existingDoc = await prisma.medicalDocument.findFirst({
      where: { checksum, uploadedById: userId }
    });

    if (existingDoc) {
      return { 
        success: false, 
        error: "This document has already been uploaded" 
      };
    }

    // Create upload directory
    const uploadDir = join(process.cwd(), "uploads", "medical-documents");
    await mkdir(uploadDir, { recursive: true });

    // Save file
    const filePath = join(uploadDir, uniqueFileName);
    await writeFile(filePath, buffer);

    // Save to database
    const document = await prisma.medicalDocument.create({
      data: {
        fileName: uniqueFileName,
        originalName: file.name,
        fileSize: file.size,
        mimeType: file.type,
        documentType: documentType as any,
        filePath: `uploads/medical-documents/${uniqueFileName}`,
        uploadedById: userId,
        checksum,
        isProcessed: false,
      },
    });

    // Trigger analysis asynchronously
    triggerDocumentAnalysis(document.id, buffer, file.type, documentType as any);

    revalidatePath("/medical-documents");
    return { success: true, documentId: document.id };
  } catch (error) {
    console.error("Error uploading document:", error);
    return { 
      success: false, 
      error: "Failed to upload document" 
    };
  }
}

// Trigger document analysis
async function triggerDocumentAnalysis(
  documentId: string,
  fileBuffer: Buffer,
  mimeType: string,
  documentType: any
) {
  try {
    // Update status to processing
    await prisma.analysisResult.create({
      data: {
        documentId,
        status: "PROCESSING" as any,
      },
    });

    // Convert to base64 for Gemini
    const base64 = fileBuffer.toString("base64");

    // Analyze with Gemini
    const analysis = await analyzeMedicalDocument(base64, mimeType, documentType);
    const routing = generateSmartRouting(analysis);

    // Find suggested doctors based on specialties
    const suggestedDoctors = await prisma.user.findMany({
      where: {
        role: "DOCTOR",
        specialty: {
          in: routing.recommendedSpecialties,
        },
      },
      select: { id: true },
    });

    // Update analysis result
    await prisma.analysisResult.update({
      where: { documentId },
      data: {
        status: "COMPLETED" as any,
        extractedData: JSON.stringify(analysis),
        patientInfo: JSON.stringify(analysis.patient),
        symptoms: analysis.symptoms,
        medications: analysis.medications,
        diagnoses: analysis.diagnoses_or_keywords,
        suggestedTests: analysis.suggested_tests,
        recommendedSpecialists: analysis.recommended_specialists,
        urgencyLevel: routing.urgencyLevel,
        suggestedDoctorIds: suggestedDoctors.map(d => d.id),
        routingReason: routing.routingReason,
      },
    });

    // Mark document as processed
    await prisma.medicalDocument.update({
      where: { id: documentId },
      data: { isProcessed: true },
    });

    revalidatePath("/medical-documents");
  } catch (error) {
    console.error("Error analyzing document:", error);
    
    // Update status to failed
    await prisma.analysisResult.update({
      where: { documentId },
      data: {
        status: "FAILED" as any,
        errorMessage: error instanceof Error ? error.message : "Analysis failed",
      },
    });
  }
}

// Get user's medical documents
export async function getUserMedicalDocuments(userId: string) {
  try {
    return await prisma.medicalDocument.findMany({
      where: { uploadedById: userId },
      include: {
        analysis: true,
        uploadedBy: {
          select: { id: true, name: true, email: true },
        },
      },
      orderBy: { createdAt: "desc" },
    });
  } catch (error) {
    console.error("Error fetching documents:", error);
    throw new Error("Failed to fetch documents");
  }
}

// Get document analysis result
export async function getDocumentAnalysis(documentId: string) {
  try {
    return await prisma.analysisResult.findUnique({
      where: { documentId },
      include: {
        document: {
          include: {
            uploadedBy: {
              select: { id: true, name: true, email: true },
            },
          },
        },
      },
    });
  } catch (error) {
    console.error("Error fetching analysis:", error);
    throw new Error("Failed to fetch analysis");
  }
}

// Delete medical document
export async function deleteMedicalDocument(documentId: string, userId: string) {
  try {
    // Verify ownership
    const document = await prisma.medicalDocument.findFirst({
      where: { id: documentId, uploadedById: userId },
    });

    if (!document) {
      throw new Error("Document not found or access denied");
    }

    // Delete from database (cascade will handle analysis)
    await prisma.medicalDocument.delete({
      where: { id: documentId },
    });

    // TODO: Delete physical file
    // await unlink(document.filePath);

    revalidatePath("/medical-documents");
    return { success: true };
  } catch (error) {
    console.error("Error deleting document:", error);
    throw new Error("Failed to delete document");
  }
}

// Get smart routing recommendations for documents
export async function getSmartRoutingRecommendations(documentIds: string[]) {
  try {
    const analyses = await prisma.analysisResult.findMany({
      where: {
        documentId: { in: documentIds },
        status: "COMPLETED",
      },
      include: {
        document: true,
      },
    });

    // Aggregate recommendations from all documents
    const allSpecialties = new Set<string>();
    const allSymptoms = new Set<string>();
    const allDiagnoses = new Set<string>();
    let maxUrgency = "LOW";

    analyses.forEach(analysis => {
      analysis.recommendedSpecialists.forEach(spec => allSpecialties.add(spec));
      analysis.symptoms.forEach(symptom => allSymptoms.add(symptom));
      analysis.diagnoses.forEach(diagnosis => allDiagnoses.add(diagnosis));
      
      if (analysis.urgencyLevel === "HIGH" || maxUrgency !== "HIGH") {
        if (analysis.urgencyLevel === "MEDIUM" && maxUrgency === "LOW") {
          maxUrgency = "MEDIUM";
        } else if (analysis.urgencyLevel === "HIGH") {
          maxUrgency = "HIGH";
        }
      }
    });

    // Get suggested doctors
    const suggestedDoctorIds = analyses.flatMap(a => a.suggestedDoctorIds);
    const uniqueDoctorIds = [...new Set(suggestedDoctorIds)];

    const doctors = await prisma.user.findMany({
      where: {
        id: { in: uniqueDoctorIds },
        role: "DOCTOR",
      },
      select: {
        id: true,
        name: true,
        email: true,
        specialty: true,
        yearsOfExperience: true,
        consultationFee: true,
      },
    });

    return {
      recommendedDoctors: doctors,
      aggregatedSymptoms: Array.from(allSymptoms),
      aggregatedDiagnoses: Array.from(allDiagnoses),
      urgencyLevel: maxUrgency,
      documentCount: analyses.length,
    };
  } catch (error) {
    console.error("Error getting routing recommendations:", error);
    throw new Error("Failed to get routing recommendations");
  }
}

// Batch upload multiple documents
export async function batchUploadDocuments(
  files: File[],
  userId: string,
  documentTypes: string[]
): Promise<{ success: boolean; results: any[]; errors: string[] }> {
  const results = [];
  const errors = [];

  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    const documentType = documentTypes[i];

    const formData = new FormData();
    formData.append("file", file);

    try {
      const result = await uploadMedicalDocument(formData, userId, documentType);
      results.push({ fileName: file.name, ...result });
    } catch (error) {
      const errorMsg = `Failed to upload ${file.name}: ${error}`;
      errors.push(errorMsg);
      results.push({ fileName: file.name, success: false, error: errorMsg });
    }
  }

  return {
    success: errors.length === 0,
    results,
    errors,
  };
}

// Get analysis progress for documents
export async function getAnalysisProgress(documentIds: string[]): Promise<DocumentUploadProgress[]> {
  try {
    const analyses = await prisma.analysisResult.findMany({
      where: { documentId: { in: documentIds } },
      include: {
        document: {
          select: { originalName: true },
        },
      },
    });

    return analyses.map(analysis => ({
      fileId: analysis.documentId,
      fileName: analysis.document.originalName,
      progress: getProgressFromStatus(analysis.status as any),
      status: mapAnalysisStatusToProgress(analysis.status as any),
      error: analysis.errorMessage || undefined,
    }));
  } catch (error) {
    console.error("Error getting analysis progress:", error);
    return [];
  }
}

// Helper functions
function getProgressFromStatus(status: string): number {
  switch (status) {
    case "PENDING": return 0;
    case "PROCESSING": return 50;
    case "COMPLETED": return 100;
    case "FAILED": return 0;
    default: return 0;
  }
}

function mapAnalysisStatusToProgress(status: string): DocumentUploadProgress["status"] {
  switch (status) {
    case "PENDING": return "uploading";
    case "PROCESSING": return "analyzing";
    case "COMPLETED": return "completed";
    case "FAILED": return "error";
    default: return "uploading";
  }
}
