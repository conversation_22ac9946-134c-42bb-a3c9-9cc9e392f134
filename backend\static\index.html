<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>IntelliCure API</title>
  <meta name="description"
    content="A powerful API for medical image analysis and disease prediction, built with cutting-edge AI models." />
  <meta name="keywords" content="IntelliCure, API, medical image analysis, disease prediction, AI models, brain tumor detection, pneumonia diagnosis, Alzheimer's analysis, disease prediction" />
  <meta name="author" content="FrostByte" />
  <meta name="theme-color" content="#10B981" />
  <link rel="icon" href="https://emjcn.arghya.dev/e/⚕️" type="image/svg+xml" />

  <!-- Tailwind CSS -->
  <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>


  <!-- Geist & Geist Mono Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Geist+Mono:wght@100..900&family=Geist:wght@100..900&display=swap"
    rel="stylesheet">

  <style>
    body {
      background: linear-gradient(135deg, #ecfdf5, #f0fdf4);
      font-family: 'Geist', sans-serif;
    }

    .glass {
      backdrop-filter: blur(10px);
      background: rgba(255, 255, 255, 0.55);
      border: 1px solid rgba(0, 0, 0, 0.05);
    }

    code {
      font-family: 'Geist Mono', monospace;
    }
  </style>
</head>

<body class="min-h-screen flex flex-col justify-center items-center text-gray-800 relative">
  <main class="w-full max-w-2xl px-6 text-center">
    <h1 class="text-4xl sm:text-5xl font-black mb-4">
      ⚕️IntelliCure API
    </h1>
    <p class="text-lg sm:text-xl mx-auto max-w-xl text-gray-700 mb-8 leading-relaxed">
      A powerful API for medical image analysis and disease prediction, built with cutting-edge AI models.
    </p>

    <div class="glass rounded-xl shadow-lg p-6 space-y-4 text-left">
      <div class="text-sm sm:text-base space-y-2">
        <p>
          <code class="text-emerald-700 font-semibold">🧠 /tumor/classify</code>
          – Detect brain tumors from MRI scans
        </p>
        <p>
          <code class="text-emerald-700 font-semibold">🫁 /pneumonia/predict</code>
          – Diagnose pneumonia from chest X-rays
        </p>
        <p>
          <code class="text-emerald-700 font-semibold">🧓 /alzheimers/analyze</code>
          – Analyze Alzheimer’s stage from brain scans
        </p>
        <p>
          <code class="text-emerald-700 font-semibold">💊 /disease/predict</code>
          – Predict disease based on symptoms
        </p>
      </div>
    </div>

    <a href="/docs"
      class="mt-10 inline-block bg-emerald-600 hover:bg-emerald-700 text-white font-semibold py-3 px-6 rounded-lg shadow-md transition duration-200">
      View API Docs →
    </a>
  </main>

  <footer class="mt-12 mb-4 text-xs text-gray-500 text-center">
    Built with ❤️ by ❄️FrostByte
  </footer>
</body>

</html>