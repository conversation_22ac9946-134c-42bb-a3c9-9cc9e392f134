module.exports = {

"[project]/node_modules/better-auth/dist/chunks/bun-sqlite-dialect.mjs [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_better-auth_dist_chunks_bun-sqlite-dialect_mjs_7bc57080._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/better-auth/dist/chunks/bun-sqlite-dialect.mjs [app-rsc] (ecmascript)");
    });
});
}}),

};