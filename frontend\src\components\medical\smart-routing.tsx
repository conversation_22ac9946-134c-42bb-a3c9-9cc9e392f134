"use client"

import { useState, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>ge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { 
  Brain, 
  Clock, 
  AlertTriangle, 
  User, 
  Star,
  Calendar,
  DollarSign,
  ArrowRight,
  Stethoscope,
  FileText,
  Activity
} from "lucide-react"
import { RoutingRecommendation, MedicalContext } from "@/types/medical-documents"
import { DoctorSpecialtyLabels } from "@/types/doctors"

interface SmartRoutingProps {
  recommendations: RoutingRecommendation[]
  medicalContext: MedicalContext
  onBookAppointment: (doctorId: string, context: MedicalContext) => void
  isLoading?: boolean
}

export function SmartRouting({
  recommendations,
  medicalContext,
  onBookAppointment,
  isLoading = false
}: SmartRoutingProps) {
  const [selectedDoctor, setSelectedDoctor] = useState<string | null>(null)

  const getUrgencyColor = (urgency: string) => {
    switch (urgency.toLowerCase()) {
      case "high":
      case "critical":
        return "destructive"
      case "medium":
        return "default"
      case "low":
      default:
        return "secondary"
    }
  }

  const getUrgencyIcon = (urgency: string) => {
    switch (urgency.toLowerCase()) {
      case "high":
      case "critical":
        return <AlertTriangle className="w-4 h-4" />
      case "medium":
        return <Clock className="w-4 h-4" />
      case "low":
      default:
        return <Activity className="w-4 h-4" />
    }
  }

  const formatMatchScore = (score: number) => {
    return Math.round(score * 100)
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Brain className="w-5 h-5 text-blue-600 animate-pulse" />
            <span>Analyzing Medical Context...</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Medical Context Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="w-5 h-5 text-blue-600" />
            <span>Medical Context Summary</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300 mb-2">
                Key Symptoms
              </h4>
              <div className="flex flex-wrap gap-1">
                {medicalContext.symptoms.slice(0, 5).map((symptom, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {symptom}
                  </Badge>
                ))}
                {medicalContext.symptoms.length > 5 && (
                  <Badge variant="outline" className="text-xs">
                    +{medicalContext.symptoms.length - 5} more
                  </Badge>
                )}
              </div>
            </div>

            <div>
              <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300 mb-2">
                Diagnoses
              </h4>
              <div className="flex flex-wrap gap-1">
                {medicalContext.diagnoses.slice(0, 3).map((diagnosis, index) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {diagnosis}
                  </Badge>
                ))}
                {medicalContext.diagnoses.length > 3 && (
                  <Badge variant="secondary" className="text-xs">
                    +{medicalContext.diagnoses.length - 3} more
                  </Badge>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between pt-2">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600 dark:text-gray-400">
                Urgency Level:
              </span>
              <Badge variant={getUrgencyColor(medicalContext.urgencyLevel)} className="flex items-center space-x-1">
                {getUrgencyIcon(medicalContext.urgencyLevel)}
                <span>{medicalContext.urgencyLevel}</span>
              </Badge>
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">
              Based on {medicalContext.documentIds.length} document(s)
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Doctor Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Stethoscope className="w-5 h-5 text-green-600" />
            <span>Recommended Specialists</span>
          </CardTitle>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            AI-powered recommendations based on your medical documents
          </p>
        </CardHeader>
        <CardContent>
          {recommendations.length === 0 ? (
            <div className="text-center py-8">
              <User className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">
                No specialist recommendations available at this time.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {recommendations.map((recommendation) => (
                <Card 
                  key={recommendation.doctorId}
                  className={`cursor-pointer transition-all duration-200 ${
                    selectedDoctor === recommendation.doctorId
                      ? "ring-2 ring-blue-500 border-blue-500"
                      : "hover:shadow-md"
                  }`}
                  onClick={() => setSelectedDoctor(recommendation.doctorId)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4 flex-1">
                        <Avatar className="w-12 h-12">
                          <AvatarImage src={`/api/avatar/${recommendation.doctor.id}`} />
                          <AvatarFallback>
                            {recommendation.doctor.name?.split(' ').map(n => n[0]).join('') || 'DR'}
                          </AvatarFallback>
                        </Avatar>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-1">
                            <h3 className="font-semibold text-gray-900 dark:text-white">
                              Dr. {recommendation.doctor.name}
                            </h3>
                            <Badge variant="outline" className="text-xs">
                              {formatMatchScore(recommendation.matchScore)}% match
                            </Badge>
                          </div>

                          <div className="space-y-2">
                            {recommendation.doctor.specialty && (
                              <div className="flex items-center space-x-2">
                                <Badge variant="secondary" className="text-xs">
                                  {DoctorSpecialtyLabels[recommendation.doctor.specialty]}
                                </Badge>
                                {recommendation.doctor.yearsOfExperience && (
                                  <span className="text-xs text-gray-500">
                                    {recommendation.doctor.yearsOfExperience} years exp.
                                  </span>
                                )}
                              </div>
                            )}

                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {recommendation.reason}
                            </p>

                            <div className="flex items-center space-x-4 text-xs text-gray-500">
                              {recommendation.doctor.consultationFee && (
                                <div className="flex items-center space-x-1">
                                  <DollarSign className="w-3 h-3" />
                                  <span>${recommendation.doctor.consultationFee}</span>
                                </div>
                              )}
                              {recommendation.availableSlots && recommendation.availableSlots.length > 0 && (
                                <div className="flex items-center space-x-1">
                                  <Calendar className="w-3 h-3" />
                                  <span>Next: {new Date(recommendation.availableSlots[0]).toLocaleDateString()}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col items-end space-y-2">
                        <Badge 
                          variant={getUrgencyColor(recommendation.urgencyLevel)}
                          className="text-xs"
                        >
                          {recommendation.urgencyLevel} Priority
                        </Badge>

                        <Button
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            onBookAppointment(recommendation.doctorId, medicalContext)
                          }}
                          className="flex items-center space-x-1"
                        >
                          <Calendar className="w-3 h-3" />
                          <span>Book</span>
                          <ArrowRight className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Key Findings */}
      {medicalContext.keyFindings.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Brain className="w-5 h-5 text-purple-600" />
              <span>Key Medical Findings</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {medicalContext.keyFindings.map((finding, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-sm text-gray-700 dark:text-gray-300">{finding}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recommended Actions */}
      {medicalContext.recommendedActions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="w-5 h-5 text-orange-600" />
              <span>Recommended Actions</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {medicalContext.recommendedActions.map((action, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 border-l-4 border-orange-500 bg-orange-50 dark:bg-orange-900/20">
                  <ArrowRight className="w-4 h-4 text-orange-600 mt-0.5 flex-shrink-0" />
                  <p className="text-sm text-gray-700 dark:text-gray-300">{action}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
