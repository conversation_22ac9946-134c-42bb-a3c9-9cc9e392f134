{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 122, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Progress({\r\n  className,\r\n  value,\r\n  ...props\r\n}: React.ComponentProps<typeof ProgressPrimitive.Root>) {\r\n  return (\r\n    <ProgressPrimitive.Root\r\n      data-slot=\"progress\"\r\n      className={cn(\r\n        \"bg-primary/20 relative h-2 w-full overflow-hidden rounded-full\",\r\n        className\r\n      )}\r\n      {...props}\r\n    >\r\n      <ProgressPrimitive.Indicator\r\n        data-slot=\"progress-indicator\"\r\n        className=\"bg-primary h-full w-full flex-1 transition-all\"\r\n        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\r\n      />\r\n    </ProgressPrimitive.Root>\r\n  )\r\n}\r\n\r\nexport { Progress }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,SAAS,EAChB,SAAS,EACT,KAAK,EACL,GAAG,OACiD;IACpD,qBACE,6LAAC,uKAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uKAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIlE;KArBS", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,6LAAC,qKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,6LAAC,qKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,6LAAC,qKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,qKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,6LAAC,qKAAA,CAAA,SAAsB;kBACrB,cAAA,6LAAC,qKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,qKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,qKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,6LAAC,qKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,qKAAA,CAAA,gBAA6B;8BAC5B,cAAA,6LAAC,2MAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,qKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,6LAAC,qKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,6LAAC,qKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,uNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,6LAAC,qKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,2NAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 468, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/lib/document-config.ts"], "sourcesContent": ["import { DocumentType } from \"@prisma/client\";\nimport { DocumentTypeConfig, DocumentSystemConfig } from \"@/types/medical-documents\";\n\n// Document type configurations\nexport const DOCUMENT_TYPE_CONFIGS: DocumentTypeConfig[] = [\n  {\n    type: DocumentType.PRESCRIPTION,\n    label: \"Prescription\",\n    description: \"Medical prescriptions and medication orders\",\n    acceptedMimeTypes: [\"image/jpeg\", \"image/png\", \"image/webp\", \"application/pdf\"],\n    maxSize: 10 * 1024 * 1024, // 10MB\n    icon: \"pill\",\n    analysisPrompt: `\n      Analyze this prescription document and extract:\n      - Patient information (name, age, gender)\n      - Prescribed medications with dosages\n      - Doctor information\n      - Date of prescription\n      - Any special instructions\n      - Identify potential drug interactions or concerns\n    `\n  },\n  {\n    type: DocumentType.LAB_REPORT,\n    label: \"Lab Report\",\n    description: \"Laboratory test results and pathology reports\",\n    acceptedMimeTypes: [\"image/jpeg\", \"image/png\", \"image/webp\", \"application/pdf\"],\n    maxSize: 15 * 1024 * 1024, // 15MB\n    icon: \"test-tube\",\n    analysisPrompt: `\n      Analyze this lab report and extract:\n      - Patient information\n      - Test types and results\n      - Reference ranges and abnormal values\n      - Test dates\n      - Recommendations or follow-up needed\n      - Urgency level based on critical values\n    `\n  },\n  {\n    type: DocumentType.MRI_REPORT,\n    label: \"MRI Report\",\n    description: \"MRI scan results and radiological reports\",\n    acceptedMimeTypes: [\"image/jpeg\", \"image/png\", \"image/webp\", \"application/pdf\"],\n    maxSize: 20 * 1024 * 1024, // 20MB\n    icon: \"brain\",\n    analysisPrompt: `\n      Analyze this MRI report and extract:\n      - Patient information\n      - Body part/region scanned\n      - Key findings and abnormalities\n      - Radiologist recommendations\n      - Urgency level\n      - Suggested follow-up or specialist referral\n    `\n  },\n  {\n    type: DocumentType.XRAY_REPORT,\n    label: \"X-Ray Report\",\n    description: \"X-ray images and radiological interpretations\",\n    acceptedMimeTypes: [\"image/jpeg\", \"image/png\", \"image/webp\", \"application/pdf\"],\n    maxSize: 15 * 1024 * 1024, // 15MB\n    icon: \"x-ray\",\n    analysisPrompt: `\n      Analyze this X-ray report and extract:\n      - Patient information\n      - Body part examined\n      - Findings and abnormalities\n      - Fractures, infections, or other conditions\n      - Recommendations for treatment\n      - Need for urgent care\n    `\n  },\n  {\n    type: DocumentType.DISCHARGE_SUMMARY,\n    label: \"Discharge Summary\",\n    description: \"Hospital discharge summaries and care instructions\",\n    acceptedMimeTypes: [\"image/jpeg\", \"image/png\", \"image/webp\", \"application/pdf\"],\n    maxSize: 10 * 1024 * 1024, // 10MB\n    icon: \"file-text\",\n    analysisPrompt: `\n      Analyze this discharge summary and extract:\n      - Patient information\n      - Admission and discharge dates\n      - Primary and secondary diagnoses\n      - Procedures performed\n      - Medications prescribed\n      - Follow-up instructions\n      - Warning signs to watch for\n    `\n  },\n  {\n    type: DocumentType.MEDICAL_CERTIFICATE,\n    label: \"Medical Certificate\",\n    description: \"Medical certificates and fitness reports\",\n    acceptedMimeTypes: [\"image/jpeg\", \"image/png\", \"image/webp\", \"application/pdf\"],\n    maxSize: 5 * 1024 * 1024, // 5MB\n    icon: \"certificate\",\n    analysisPrompt: `\n      Analyze this medical certificate and extract:\n      - Patient information\n      - Medical condition or fitness status\n      - Validity period\n      - Restrictions or recommendations\n      - Doctor information and signature\n    `\n  },\n  {\n    type: DocumentType.OTHER,\n    label: \"Other Medical Document\",\n    description: \"Other medical documents and reports\",\n    acceptedMimeTypes: [\"image/jpeg\", \"image/png\", \"image/webp\", \"application/pdf\"],\n    maxSize: 10 * 1024 * 1024, // 10MB\n    icon: \"file\",\n    analysisPrompt: `\n      Analyze this medical document and extract:\n      - Document type and purpose\n      - Patient information\n      - Key medical information\n      - Dates and healthcare provider details\n      - Any recommendations or follow-up needed\n    `\n  }\n];\n\n// System configuration\nexport const DOCUMENT_SYSTEM_CONFIG: DocumentSystemConfig = {\n  maxFileSize: 20 * 1024 * 1024, // 20MB max\n  maxFilesPerUpload: 10,\n  supportedTypes: DOCUMENT_TYPE_CONFIGS,\n  storageProvider: \"local\", // Can be configured via env\n  analysisProvider: \"gemini\",\n  autoAnalysis: true,\n  retentionPeriod: 365, // 1 year\n};\n\n// Accepted file types for dropzone\nexport const ACCEPTED_FILE_TYPES = {\n  \"image/jpeg\": [\".jpg\", \".jpeg\"],\n  \"image/png\": [\".png\"],\n  \"image/webp\": [\".webp\"],\n  \"application/pdf\": [\".pdf\"],\n};\n\n// File type validation\nexport function validateFileType(file: File): boolean {\n  return Object.keys(ACCEPTED_FILE_TYPES).includes(file.type);\n}\n\n// File size validation\nexport function validateFileSize(file: File, maxSize?: number): boolean {\n  const limit = maxSize || DOCUMENT_SYSTEM_CONFIG.maxFileSize;\n  return file.size <= limit;\n}\n\n// Get document type config\nexport function getDocumentTypeConfig(type: DocumentType): DocumentTypeConfig | undefined {\n  return DOCUMENT_TYPE_CONFIGS.find(config => config.type === type);\n}\n\n// Format file size for display\nexport function formatFileSize(bytes: number): string {\n  if (bytes === 0) return \"0 Bytes\";\n  const k = 1024;\n  const sizes = [\"Bytes\", \"KB\", \"MB\", \"GB\"];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\n\n// Get file extension\nexport function getFileExtension(filename: string): string {\n  return filename.slice((filename.lastIndexOf(\".\") - 1 >>> 0) + 2);\n}\n\n// Generate unique filename\nexport function generateUniqueFilename(originalName: string): string {\n  const timestamp = Date.now();\n  const random = Math.random().toString(36).substring(2, 15);\n  const extension = getFileExtension(originalName);\n  return `${timestamp}_${random}.${extension}`;\n}\n\n// Check if file is an image\nexport function isImageFile(mimeType: string): boolean {\n  return mimeType.startsWith(\"image/\");\n}\n\n// Check if file is a PDF\nexport function isPdfFile(mimeType: string): boolean {\n  return mimeType === \"application/pdf\";\n}\n\n// Get appropriate icon for file type\nexport function getFileTypeIcon(mimeType: string): string {\n  if (isImageFile(mimeType)) return \"image\";\n  if (isPdfFile(mimeType)) return \"file-text\";\n  return \"file\";\n}\n\n// Document type display labels\nexport const DOCUMENT_TYPE_LABELS: Record<DocumentType, string> = {\n  [DocumentType.PRESCRIPTION]: \"Prescription\",\n  [DocumentType.LAB_REPORT]: \"Lab Report\",\n  [DocumentType.MRI_REPORT]: \"MRI Report\",\n  [DocumentType.XRAY_REPORT]: \"X-Ray Report\",\n  [DocumentType.DISCHARGE_SUMMARY]: \"Discharge Summary\",\n  [DocumentType.MEDICAL_CERTIFICATE]: \"Medical Certificate\",\n  [DocumentType.OTHER]: \"Other Document\",\n};\n\n// Priority levels for document processing\nexport const DOCUMENT_PRIORITY_LEVELS = {\n  CRITICAL: { value: 1, label: \"Critical\", color: \"red\" },\n  HIGH: { value: 2, label: \"High\", color: \"orange\" },\n  NORMAL: { value: 3, label: \"Normal\", color: \"blue\" },\n  LOW: { value: 4, label: \"Low\", color: \"gray\" },\n};\n\n// Analysis stage descriptions\nexport const ANALYSIS_STAGES = [\n  { threshold: 10, label: \"Document Reading\", description: \"Reading and parsing document content\" },\n  { threshold: 25, label: \"Content Processing\", description: \"Processing text and image content\" },\n  { threshold: 45, label: \"Medical Term Analysis\", description: \"Analyzing medical terminology and context\" },\n  { threshold: 65, label: \"Clinical Data Review\", description: \"Reviewing clinical data and findings\" },\n  { threshold: 85, label: \"Generating Recommendations\", description: \"Generating recommendations and routing suggestions\" },\n  { threshold: 100, label: \"Finalizing Results\", description: \"Finalizing analysis results\" },\n];\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;;AAIO,MAAM,wBAA8C;IACzD;QACE,MAAM,yJAAA,CAAA,eAAY,CAAC,YAAY;QAC/B,OAAO;QACP,aAAa;QACb,mBAAmB;YAAC;YAAc;YAAa;YAAc;SAAkB;QAC/E,SAAS,KAAK,OAAO;QACrB,MAAM;QACN,gBAAgB,CAAC;;;;;;;;IAQjB,CAAC;IACH;IACA;QACE,MAAM,yJAAA,CAAA,eAAY,CAAC,UAAU;QAC7B,OAAO;QACP,aAAa;QACb,mBAAmB;YAAC;YAAc;YAAa;YAAc;SAAkB;QAC/E,SAAS,KAAK,OAAO;QACrB,MAAM;QACN,gBAAgB,CAAC;;;;;;;;IAQjB,CAAC;IACH;IACA;QACE,MAAM,yJAAA,CAAA,eAAY,CAAC,UAAU;QAC7B,OAAO;QACP,aAAa;QACb,mBAAmB;YAAC;YAAc;YAAa;YAAc;SAAkB;QAC/E,SAAS,KAAK,OAAO;QACrB,MAAM;QACN,gBAAgB,CAAC;;;;;;;;IAQjB,CAAC;IACH;IACA;QACE,MAAM,yJAAA,CAAA,eAAY,CAAC,WAAW;QAC9B,OAAO;QACP,aAAa;QACb,mBAAmB;YAAC;YAAc;YAAa;YAAc;SAAkB;QAC/E,SAAS,KAAK,OAAO;QACrB,MAAM;QACN,gBAAgB,CAAC;;;;;;;;IAQjB,CAAC;IACH;IACA;QACE,MAAM,yJAAA,CAAA,eAAY,CAAC,iBAAiB;QACpC,OAAO;QACP,aAAa;QACb,mBAAmB;YAAC;YAAc;YAAa;YAAc;SAAkB;QAC/E,SAAS,KAAK,OAAO;QACrB,MAAM;QACN,gBAAgB,CAAC;;;;;;;;;IASjB,CAAC;IACH;IACA;QACE,MAAM,yJAAA,CAAA,eAAY,CAAC,mBAAmB;QACtC,OAAO;QACP,aAAa;QACb,mBAAmB;YAAC;YAAc;YAAa;YAAc;SAAkB;QAC/E,SAAS,IAAI,OAAO;QACpB,MAAM;QACN,gBAAgB,CAAC;;;;;;;IAOjB,CAAC;IACH;IACA;QACE,MAAM,yJAAA,CAAA,eAAY,CAAC,KAAK;QACxB,OAAO;QACP,aAAa;QACb,mBAAmB;YAAC;YAAc;YAAa;YAAc;SAAkB;QAC/E,SAAS,KAAK,OAAO;QACrB,MAAM;QACN,gBAAgB,CAAC;;;;;;;IAOjB,CAAC;IACH;CACD;AAGM,MAAM,yBAA+C;IAC1D,aAAa,KAAK,OAAO;IACzB,mBAAmB;IACnB,gBAAgB;IAChB,iBAAiB;IACjB,kBAAkB;IAClB,cAAc;IACd,iBAAiB;AACnB;AAGO,MAAM,sBAAsB;IACjC,cAAc;QAAC;QAAQ;KAAQ;IAC/B,aAAa;QAAC;KAAO;IACrB,cAAc;QAAC;KAAQ;IACvB,mBAAmB;QAAC;KAAO;AAC7B;AAGO,SAAS,iBAAiB,IAAU;IACzC,OAAO,OAAO,IAAI,CAAC,qBAAqB,QAAQ,CAAC,KAAK,IAAI;AAC5D;AAGO,SAAS,iBAAiB,IAAU,EAAE,OAAgB;IAC3D,MAAM,QAAQ,WAAW,uBAAuB,WAAW;IAC3D,OAAO,KAAK,IAAI,IAAI;AACtB;AAGO,SAAS,sBAAsB,IAAkB;IACtD,OAAO,sBAAsB,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;AAC9D;AAGO,SAAS,eAAe,KAAa;IAC1C,IAAI,UAAU,GAAG,OAAO;IACxB,MAAM,IAAI;IACV,MAAM,QAAQ;QAAC;QAAS;QAAM;QAAM;KAAK;IACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;IAChD,OAAO,OAAO,UAAU,CAAC,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;AAChF;AAGO,SAAS,iBAAiB,QAAgB;IAC/C,OAAO,SAAS,KAAK,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO,MAAM,CAAC,IAAI;AAChE;AAGO,SAAS,uBAAuB,YAAoB;IACzD,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IACvD,MAAM,YAAY,iBAAiB;IACnC,OAAO,GAAG,UAAU,CAAC,EAAE,OAAO,CAAC,EAAE,WAAW;AAC9C;AAGO,SAAS,YAAY,QAAgB;IAC1C,OAAO,SAAS,UAAU,CAAC;AAC7B;AAGO,SAAS,UAAU,QAAgB;IACxC,OAAO,aAAa;AACtB;AAGO,SAAS,gBAAgB,QAAgB;IAC9C,IAAI,YAAY,WAAW,OAAO;IAClC,IAAI,UAAU,WAAW,OAAO;IAChC,OAAO;AACT;AAGO,MAAM,uBAAqD;IAChE,CAAC,yJAAA,CAAA,eAAY,CAAC,YAAY,CAAC,EAAE;IAC7B,CAAC,yJAAA,CAAA,eAAY,CAAC,UAAU,CAAC,EAAE;IAC3B,CAAC,yJAAA,CAAA,eAAY,CAAC,UAAU,CAAC,EAAE;IAC3B,CAAC,yJAAA,CAAA,eAAY,CAAC,WAAW,CAAC,EAAE;IAC5B,CAAC,yJAAA,CAAA,eAAY,CAAC,iBAAiB,CAAC,EAAE;IAClC,CAAC,yJAAA,CAAA,eAAY,CAAC,mBAAmB,CAAC,EAAE;IACpC,CAAC,yJAAA,CAAA,eAAY,CAAC,KAAK,CAAC,EAAE;AACxB;AAGO,MAAM,2BAA2B;IACtC,UAAU;QAAE,OAAO;QAAG,OAAO;QAAY,OAAO;IAAM;IACtD,MAAM;QAAE,OAAO;QAAG,OAAO;QAAQ,OAAO;IAAS;IACjD,QAAQ;QAAE,OAAO;QAAG,OAAO;QAAU,OAAO;IAAO;IACnD,KAAK;QAAE,OAAO;QAAG,OAAO;QAAO,OAAO;IAAO;AAC/C;AAGO,MAAM,kBAAkB;IAC7B;QAAE,WAAW;QAAI,OAAO;QAAoB,aAAa;IAAuC;IAChG;QAAE,WAAW;QAAI,OAAO;QAAsB,aAAa;IAAoC;IAC/F;QAAE,WAAW;QAAI,OAAO;QAAyB,aAAa;IAA4C;IAC1G;QAAE,WAAW;QAAI,OAAO;QAAwB,aAAa;IAAuC;IACpG;QAAE,WAAW;QAAI,OAAO;QAA8B,aAAa;IAAqD;IACxH;QAAE,WAAW;QAAK,OAAO;QAAsB,aAAa;IAA8B;CAC3F", "debugId": null}}, {"offset": {"line": 780, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/medical/document-upload.tsx"], "sourcesContent": ["\"use client\";\n\nimport { use<PERSON><PERSON>back, useState } from \"react\";\nimport { useDropzone } from \"react-dropzone\";\nimport {\n  Upload,\n  FileText,\n  ImageIcon,\n  AlertCircle,\n  X,\n  Eye,\n  Brain,\n  CheckCircle2,\n} from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Progress } from \"@/components/ui/progress\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { DocumentType } from \"@prisma/client\";\nimport {\n  UploadedFile,\n  DocumentUploadProgress,\n  DocumentTypeConfig,\n} from \"@/types/medical-documents\";\nimport {\n  ACCEPTED_FILE_TYPES,\n  DOCUMENT_TYPE_CONFIGS,\n  DOCUMENT_SYSTEM_CONFIG,\n  formatFileSize,\n  validateFileType,\n  validateFileSize,\n  getFileTypeIcon,\n  DOCUMENT_TYPE_LABELS,\n} from \"@/lib/document-config\";\n\ninterface DocumentUploadProps {\n  onFileUpload: (files: File[], documentType: DocumentType) => void;\n  onFileRemove: (fileId: string) => void;\n  uploadProgress: DocumentUploadProgress[];\n  uploadedFiles: UploadedFile[];\n  isUploading: boolean;\n  maxFiles?: number;\n  autoAnalysis?: boolean;\n}\n\nexport function DocumentUpload({\n  onFileUpload,\n  onFileRemove,\n  uploadProgress,\n  uploadedFiles,\n  isUploading,\n  maxFiles = DOCUMENT_SYSTEM_CONFIG.maxFilesPerUpload,\n  autoAnalysis = true,\n}: DocumentUploadProps) {\n  const [selectedDocumentType, setSelectedDocumentType] =\n    useState<DocumentType>(DocumentType.PRESCRIPTION);\n  const [draggedFiles, setDraggedFiles] = useState<File[]>([]);\n\n  const selectedConfig = DOCUMENT_TYPE_CONFIGS.find(\n    (config) => config.type === selectedDocumentType\n  );\n\n  const onDrop = useCallback(\n    (acceptedFiles: File[], rejectedFiles: any[]) => {\n      if (rejectedFiles.length > 0) {\n        console.log(\"Rejected files:\", rejectedFiles);\n        // TODO: Show error toast for rejected files\n      }\n\n      if (acceptedFiles.length > 0) {\n        // Validate files\n        const validFiles = acceptedFiles.filter((file) => {\n          const isValidType = validateFileType(file);\n          const isValidSize = validateFileSize(file, selectedConfig?.maxSize);\n          return isValidType && isValidSize;\n        });\n\n        if (validFiles.length > 0) {\n          onFileUpload(validFiles, selectedDocumentType);\n        }\n      }\n      setDraggedFiles([]);\n    },\n    [onFileUpload, selectedDocumentType, selectedConfig?.maxSize]\n  );\n\n  const { getRootProps, getInputProps, isDragActive, isDragReject } =\n    useDropzone({\n      onDrop,\n      accept: ACCEPTED_FILE_TYPES,\n      maxSize: selectedConfig?.maxSize || DOCUMENT_SYSTEM_CONFIG.maxFileSize,\n      maxFiles: maxFiles - uploadedFiles.length,\n      disabled: isUploading || uploadedFiles.length >= maxFiles,\n      onDragEnter: (event) => {\n        const files = Array.from(event.dataTransfer?.files || []);\n        setDraggedFiles(files);\n      },\n      onDragLeave: () => {\n        setDraggedFiles([]);\n      },\n    });\n\n  const getIcon = () => {\n    if (isDragActive && !isDragReject) {\n      return (\n        <Upload className=\"w-12 h-12 text-blue-600 dark:text-blue-400 animate-bounce\" />\n      );\n    }\n    if (isDragReject) {\n      return (\n        <AlertCircle className=\"w-12 h-12 text-red-500 dark:text-red-400\" />\n      );\n    }\n    return <Upload className=\"w-12 h-12 text-gray-400 dark:text-gray-500\" />;\n  };\n\n  const getMessage = () => {\n    if (isDragReject) {\n      return \"File type not supported or file too large\";\n    }\n    if (isDragActive) {\n      return `Drop your ${DOCUMENT_TYPE_LABELS[\n        selectedDocumentType\n      ].toLowerCase()} here...`;\n    }\n    return `Drag & drop your ${DOCUMENT_TYPE_LABELS[\n      selectedDocumentType\n    ].toLowerCase()} here`;\n  };\n\n  const getFileIcon = (type: string) => {\n    const iconName = getFileTypeIcon(type);\n    if (iconName === \"image\") {\n      return <ImageIcon className=\"w-5 h-5 text-blue-600 dark:text-blue-400\" />;\n    }\n    return <FileText className=\"w-5 h-5 text-red-600 dark:text-red-400\" />;\n  };\n\n  const getFileTypeLabel = (type: string) => {\n    if (type === \"application/pdf\") return \"PDF\";\n    if (type.startsWith(\"image/\")) return type.split(\"/\")[1].toUpperCase();\n    return \"Unknown\";\n  };\n\n  const getProgressColor = (progress: number) => {\n    if (progress < 25) return \"bg-blue-600 dark:bg-blue-500\";\n    if (progress < 50) return \"bg-purple-600 dark:bg-purple-500\";\n    if (progress < 75) return \"bg-orange-600 dark:bg-orange-500\";\n    return \"bg-green-600 dark:bg-green-500\";\n  };\n\n  const getStatusIcon = (status: DocumentUploadProgress[\"status\"]) => {\n    switch (status) {\n      case \"uploading\":\n        return <Upload className=\"w-4 h-4 text-blue-600 animate-pulse\" />;\n      case \"processing\":\n        return <FileText className=\"w-4 h-4 text-purple-600 animate-pulse\" />;\n      case \"analyzing\":\n        return <Brain className=\"w-4 h-4 text-orange-600 animate-pulse\" />;\n      case \"completed\":\n        return <CheckCircle2 className=\"w-4 h-4 text-green-600\" />;\n      case \"error\":\n        return <AlertCircle className=\"w-4 h-4 text-red-600\" />;\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Document Type Selection */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-lg\">Document Type</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <Select\n            value={selectedDocumentType}\n            onValueChange={(value) =>\n              setSelectedDocumentType(value as DocumentType)\n            }\n            disabled={isUploading}\n          >\n            <SelectTrigger className=\"w-full\">\n              <SelectValue placeholder=\"Select document type\" />\n            </SelectTrigger>\n            <SelectContent>\n              {DOCUMENT_TYPE_CONFIGS.map((config) => (\n                <SelectItem key={config.type} value={config.type}>\n                  <div className=\"flex items-center space-x-2\">\n                    <span>{config.label}</span>\n                    <span className=\"text-xs text-muted-foreground\">\n                      - {config.description}\n                    </span>\n                  </div>\n                </SelectItem>\n              ))}\n            </SelectContent>\n          </Select>\n\n          {selectedConfig && (\n            <div className=\"mt-3 p-3 bg-muted rounded-lg\">\n              <p className=\"text-sm text-muted-foreground\">\n                {selectedConfig.description}\n              </p>\n              <div className=\"flex items-center space-x-4 mt-2 text-xs text-muted-foreground\">\n                <span>Max size: {formatFileSize(selectedConfig.maxSize)}</span>\n                <span>Formats: PDF, JPG, PNG, WebP</span>\n              </div>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Upload Area */}\n      <Card>\n        <CardContent className=\"p-6\">\n          <div\n            {...getRootProps()}\n            className={cn(\n              \"border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-all duration-200\",\n              isDragActive &&\n                !isDragReject &&\n                \"border-blue-500 bg-blue-50 dark:bg-blue-900/30\",\n              isDragReject && \"border-red-500 bg-red-50 dark:bg-red-900/20\",\n              !isDragActive &&\n                \"border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500 hover:bg-gray-50 dark:hover:bg-gray-700/50\",\n              (isUploading || uploadedFiles.length >= maxFiles) &&\n                \"pointer-events-none opacity-70\"\n            )}\n          >\n            <input {...getInputProps()} />\n\n            <div className=\"flex flex-col items-center space-y-4\">\n              {getIcon()}\n\n              <div>\n                <p className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\n                  {getMessage()}\n                </p>\n                <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4\">\n                  or click to browse your files\n                </p>\n\n                <Button\n                  variant=\"outline\"\n                  disabled={isUploading || uploadedFiles.length >= maxFiles}\n                  className=\"dark:border-gray-600 dark:text-white dark:hover:bg-gray-700\"\n                >\n                  Choose Files\n                </Button>\n              </div>\n\n              <div className=\"flex flex-wrap justify-center gap-2 text-xs text-gray-500 dark:text-gray-400\">\n                <span className=\"flex items-center space-x-1\">\n                  <FileText className=\"w-3 h-3\" />\n                  <span>PDF</span>\n                </span>\n                <span className=\"flex items-center space-x-1\">\n                  <ImageIcon className=\"w-3 h-3\" />\n                  <span>JPG</span>\n                </span>\n                <span className=\"flex items-center space-x-1\">\n                  <ImageIcon className=\"w-3 h-3\" />\n                  <span>PNG</span>\n                </span>\n                <span>\n                  • Max{\" \"}\n                  {formatFileSize(\n                    selectedConfig?.maxSize ||\n                      DOCUMENT_SYSTEM_CONFIG.maxFileSize\n                  )}{\" \"}\n                  per file\n                </span>\n                <span>• {maxFiles - uploadedFiles.length} files remaining</span>\n              </div>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Upload Progress */}\n      {uploadProgress.length > 0 && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg flex items-center space-x-2\">\n              <Brain className=\"w-5 h-5 text-blue-600\" />\n              <span>Processing Documents</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-4\">\n            {uploadProgress.map((progress) => (\n              <div key={progress.fileId} className=\"space-y-2\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-2\">\n                    {getStatusIcon(progress.status)}\n                    <span className=\"text-sm font-medium truncate\">\n                      {progress.fileName}\n                    </span>\n                  </div>\n                  <span className=\"text-sm text-muted-foreground\">\n                    {progress.progress}%\n                  </span>\n                </div>\n\n                <div className=\"relative\">\n                  <Progress\n                    value={progress.progress}\n                    className=\"h-2 bg-gray-200 dark:bg-gray-700\"\n                  />\n                  <div\n                    className={`absolute top-0 left-0 h-2 rounded-full transition-all duration-500 ${getProgressColor(\n                      progress.progress\n                    )}`}\n                    style={{ width: `${progress.progress}%` }}\n                  />\n                </div>\n\n                {progress.error && (\n                  <p className=\"text-xs text-red-600 dark:text-red-400\">\n                    Error: {progress.error}\n                  </p>\n                )}\n              </div>\n            ))}\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Uploaded Files */}\n      {uploadedFiles.length > 0 && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg\">\n              Uploaded Documents ({uploadedFiles.length})\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-3\">\n            {uploadedFiles.map((file) => (\n              <div\n                key={file.id}\n                className=\"flex items-center justify-between p-3 border rounded-lg\"\n              >\n                <div className=\"flex items-center space-x-3 flex-1 min-w-0\">\n                  {file.preview ? (\n                    <div className=\"relative w-12 h-12 rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800 flex-shrink-0\">\n                      <img\n                        src={file.preview}\n                        alt={file.name}\n                        className=\"w-full h-full object-cover\"\n                      />\n                    </div>\n                  ) : (\n                    <div className=\"w-12 h-12 rounded-lg bg-gray-100 dark:bg-gray-800 flex items-center justify-center flex-shrink-0\">\n                      {getFileIcon(file.type)}\n                    </div>\n                  )}\n\n                  <div className=\"flex-1 min-w-0\">\n                    <p className=\"text-sm font-medium text-gray-900 dark:text-white truncate\">\n                      {file.name}\n                    </p>\n                    <div className=\"flex items-center space-x-2 mt-1\">\n                      <Badge variant=\"secondary\" className=\"text-xs\">\n                        {getFileTypeLabel(file.type)}\n                      </Badge>\n                      <span className=\"text-xs text-gray-500 dark:text-gray-400\">\n                        {formatFileSize(file.size)}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center space-x-2 flex-shrink-0\">\n                  {file.preview && (\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={() => window.open(file.preview, \"_blank\")}\n                      className=\"dark:hover:bg-gray-700\"\n                    >\n                      <Eye className=\"w-4 h-4 text-gray-700 dark:text-gray-300\" />\n                    </Button>\n                  )}\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={() => onFileRemove(file.id)}\n                    className=\"text-red-600 hover:text-red-700 hover:bg-red-50 dark:text-red-400 dark:hover:text-red-300 dark:hover:bg-red-900/30\"\n                  >\n                    <X className=\"w-4 h-4\" />\n                  </Button>\n                </div>\n              </div>\n            ))}\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;AACA;AACA;AACA;AAOA;AAMA;;;AAhCA;;;;;;;;;;;;AAqDO,SAAS,eAAe,EAC7B,YAAY,EACZ,YAAY,EACZ,cAAc,EACd,aAAa,EACb,WAAW,EACX,WAAW,mIAAA,CAAA,yBAAsB,CAAC,iBAAiB,EACnD,eAAe,IAAI,EACC;;IACpB,MAAM,CAAC,sBAAsB,wBAAwB,GACnD,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,yJAAA,CAAA,eAAY,CAAC,YAAY;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAE3D,MAAM,iBAAiB,mIAAA,CAAA,wBAAqB,CAAC,IAAI,CAC/C,CAAC,SAAW,OAAO,IAAI,KAAK;IAG9B,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8CACvB,CAAC,eAAuB;YACtB,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,QAAQ,GAAG,CAAC,mBAAmB;YAC/B,4CAA4C;YAC9C;YAEA,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC5B,iBAAiB;gBACjB,MAAM,aAAa,cAAc,MAAM;qEAAC,CAAC;wBACvC,MAAM,cAAc,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE;wBACrC,MAAM,cAAc,CAAA,GAAA,mIAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,gBAAgB;wBAC3D,OAAO,eAAe;oBACxB;;gBAEA,IAAI,WAAW,MAAM,GAAG,GAAG;oBACzB,aAAa,YAAY;gBAC3B;YACF;YACA,gBAAgB,EAAE;QACpB;6CACA;QAAC;QAAc;QAAsB,gBAAgB;KAAQ;IAG/D,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,YAAY,EAAE,GAC/D,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QACV;QACA,QAAQ,mIAAA,CAAA,sBAAmB;QAC3B,SAAS,gBAAgB,WAAW,mIAAA,CAAA,yBAAsB,CAAC,WAAW;QACtE,UAAU,WAAW,cAAc,MAAM;QACzC,UAAU,eAAe,cAAc,MAAM,IAAI;QACjD,WAAW;0CAAE,CAAC;gBACZ,MAAM,QAAQ,MAAM,IAAI,CAAC,MAAM,YAAY,EAAE,SAAS,EAAE;gBACxD,gBAAgB;YAClB;;QACA,WAAW;0CAAE;gBACX,gBAAgB,EAAE;YACpB;;IACF;IAEF,MAAM,UAAU;QACd,IAAI,gBAAgB,CAAC,cAAc;YACjC,qBACE,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAEtB;QACA,IAAI,cAAc;YAChB,qBACE,6LAAC,uNAAA,CAAA,cAAW;gBAAC,WAAU;;;;;;QAE3B;QACA,qBAAO,6LAAC,yMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;IAC3B;IAEA,MAAM,aAAa;QACjB,IAAI,cAAc;YAChB,OAAO;QACT;QACA,IAAI,cAAc;YAChB,OAAO,CAAC,UAAU,EAAE,mIAAA,CAAA,uBAAoB,CACtC,qBACD,CAAC,WAAW,GAAG,QAAQ,CAAC;QAC3B;QACA,OAAO,CAAC,iBAAiB,EAAE,mIAAA,CAAA,uBAAoB,CAC7C,qBACD,CAAC,WAAW,GAAG,KAAK,CAAC;IACxB;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,kBAAe,AAAD,EAAE;QACjC,IAAI,aAAa,SAAS;YACxB,qBAAO,6LAAC,2MAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAC9B;QACA,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC7B;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,SAAS,mBAAmB,OAAO;QACvC,IAAI,KAAK,UAAU,CAAC,WAAW,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,WAAW;QACpE,OAAO;IACT;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,WAAW,IAAI,OAAO;QAC1B,IAAI,WAAW,IAAI,OAAO;QAC1B,IAAI,WAAW,IAAI,OAAO;QAC1B,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;YAC3B,KAAK;gBACH,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,wNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YACjC,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAU;;;;;;;;;;;kCAEjC,6LAAC,mIAAA,CAAA,cAAW;;0CACV,6LAAC,qIAAA,CAAA,SAAM;gCACL,OAAO;gCACP,eAAe,CAAC,QACd,wBAAwB;gCAE1B,UAAU;;kDAEV,6LAAC,qIAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,6LAAC,qIAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,6LAAC,qIAAA,CAAA,gBAAa;kDACX,mIAAA,CAAA,wBAAqB,CAAC,GAAG,CAAC,CAAC,uBAC1B,6LAAC,qIAAA,CAAA,aAAU;gDAAmB,OAAO,OAAO,IAAI;0DAC9C,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAM,OAAO,KAAK;;;;;;sEACnB,6LAAC;4DAAK,WAAU;;gEAAgC;gEAC3C,OAAO,WAAW;;;;;;;;;;;;;+CAJV,OAAO,IAAI;;;;;;;;;;;;;;;;4BAYjC,gCACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDACV,eAAe,WAAW;;;;;;kDAE7B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;oDAAK;oDAAW,CAAA,GAAA,mIAAA,CAAA,iBAAc,AAAD,EAAE,eAAe,OAAO;;;;;;;0DACtD,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,6LAAC,mIAAA,CAAA,OAAI;0BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,6LAAC;wBACE,GAAG,cAAc;wBAClB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gGACA,gBACE,CAAC,gBACD,kDACF,gBAAgB,+CAChB,CAAC,gBACC,oIACF,CAAC,eAAe,cAAc,MAAM,IAAI,QAAQ,KAC9C;;0CAGJ,6LAAC;gCAAO,GAAG,eAAe;;;;;;0CAE1B,6LAAC;gCAAI,WAAU;;oCACZ;kDAED,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DACV;;;;;;0DAEH,6LAAC;gDAAE,WAAU;0DAAgD;;;;;;0DAI7D,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,UAAU,eAAe,cAAc,MAAM,IAAI;gDACjD,WAAU;0DACX;;;;;;;;;;;;kDAKH,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;;kEACd,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;kEACpB,6LAAC;kEAAK;;;;;;;;;;;;0DAER,6LAAC;gDAAK,WAAU;;kEACd,6LAAC,2MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,6LAAC;kEAAK;;;;;;;;;;;;0DAER,6LAAC;gDAAK,WAAU;;kEACd,6LAAC,2MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;kEACrB,6LAAC;kEAAK;;;;;;;;;;;;0DAER,6LAAC;;oDAAK;oDACE;oDACL,CAAA,GAAA,mIAAA,CAAA,iBAAc,AAAD,EACZ,gBAAgB,WACd,mIAAA,CAAA,yBAAsB,CAAC,WAAW;oDACnC;oDAAI;;;;;;;0DAGT,6LAAC;;oDAAK;oDAAG,WAAW,cAAc,MAAM;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQlD,eAAe,MAAM,GAAG,mBACvB,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;kCAGV,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACpB,eAAe,GAAG,CAAC,CAAC,yBACnB,6LAAC;gCAA0B,WAAU;;kDACnC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;oDACZ,cAAc,SAAS,MAAM;kEAC9B,6LAAC;wDAAK,WAAU;kEACb,SAAS,QAAQ;;;;;;;;;;;;0DAGtB,6LAAC;gDAAK,WAAU;;oDACb,SAAS,QAAQ;oDAAC;;;;;;;;;;;;;kDAIvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uIAAA,CAAA,WAAQ;gDACP,OAAO,SAAS,QAAQ;gDACxB,WAAU;;;;;;0DAEZ,6LAAC;gDACC,WAAW,CAAC,mEAAmE,EAAE,iBAC/E,SAAS,QAAQ,GAChB;gDACH,OAAO;oDAAE,OAAO,GAAG,SAAS,QAAQ,CAAC,CAAC,CAAC;gDAAC;;;;;;;;;;;;oCAI3C,SAAS,KAAK,kBACb,6LAAC;wCAAE,WAAU;;4CAAyC;4CAC5C,SAAS,KAAK;;;;;;;;+BA5BlB,SAAS,MAAM;;;;;;;;;;;;;;;;YAsChC,cAAc,MAAM,GAAG,mBACtB,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;gCAAU;gCACR,cAAc,MAAM;gCAAC;;;;;;;;;;;;kCAG9C,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;kCACpB,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,OAAO,iBACX,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,KAAK,KAAK,OAAO;oDACjB,KAAK,KAAK,IAAI;oDACd,WAAU;;;;;;;;;;qEAId,6LAAC;gDAAI,WAAU;0DACZ,YAAY,KAAK,IAAI;;;;;;0DAI1B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEACV,KAAK,IAAI;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAY,WAAU;0EAClC,iBAAiB,KAAK,IAAI;;;;;;0EAE7B,6LAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,mIAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;kDAMjC,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,OAAO,kBACX,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,OAAO,EAAE;gDACzC,WAAU;0DAEV,cAAA,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;0DAGnB,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,aAAa,KAAK,EAAE;gDACnC,WAAU;0DAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;;+BAlDZ,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;AA4D5B;GAnWgB;;QA0CZ,2KAAA,CAAA,cAAW;;;KA1CC", "debugId": null}}, {"offset": {"line": 1608, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Tabs({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Root>) {\r\n  return (\r\n    <TabsPrimitive.Root\r\n      data-slot=\"tabs\"\r\n      className={cn(\"flex flex-col gap-2\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsList({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.List>) {\r\n  return (\r\n    <TabsPrimitive.List\r\n      data-slot=\"tabs-list\"\r\n      className={cn(\r\n        \"bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsTrigger({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Trigger>) {\r\n  return (\r\n    <TabsPrimitive.Trigger\r\n      data-slot=\"tabs-trigger\"\r\n      className={cn(\r\n        \"data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TabsContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof TabsPrimitive.Content>) {\r\n  return (\r\n    <TabsPrimitive.Content\r\n      data-slot=\"tabs-content\"\r\n      className={cn(\"flex-1 outline-none\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\r\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OAC6C;IAChD,qBACE,6LAAC,mKAAA,CAAA,OAAkB;QACjB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mqBACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,6LAAC,mKAAA,CAAA,UAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB;QACpC,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 1684, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/medical/analysis-results.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState } from \"react\"\nimport { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from \"@/components/ui/card\"\nimport { Badge } from \"@/components/ui/badge\"\nimport { Button } from \"@/components/ui/button\"\nimport { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from \"@/components/ui/tabs\"\nimport { Collapsible, CollapsibleContent, CollapsibleTrigger } from \"@/components/ui/collapsible\"\nimport { \n  Brain, \n  FileText, \n  User, \n  Pill, \n  Activity, \n  TestTube, \n  Stethoscope,\n  AlertTriangle,\n  Clock,\n  CheckCircle2,\n  ChevronDown,\n  ChevronRight,\n  Download,\n  Share,\n  Eye\n} from \"lucide-react\"\nimport { AnalysisResult, GeminiAnalysisResponse, PatientInfo } from \"@/types/medical-documents\"\nimport { DOCUMENT_TYPE_LABELS } from \"@/lib/document-config\"\n\ninterface AnalysisResultsProps {\n  analysisResult: AnalysisResult\n  onViewDocument?: () => void\n  onDownloadReport?: () => void\n  onShareResults?: () => void\n  showActions?: boolean\n}\n\nexport function AnalysisResults({\n  analysisResult,\n  onViewDocument,\n  onDownloadReport,\n  onShareResults,\n  showActions = true\n}: AnalysisResultsProps) {\n  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['overview']))\n\n  const toggleSection = (section: string) => {\n    const newExpanded = new Set(expandedSections)\n    if (newExpanded.has(section)) {\n      newExpanded.delete(section)\n    } else {\n      newExpanded.add(section)\n    }\n    setExpandedSections(newExpanded)\n  }\n\n  const parsedData: GeminiAnalysisResponse | null = analysisResult.extractedData \n    ? JSON.parse(analysisResult.extractedData) \n    : null\n\n  const parsedPatientInfo: PatientInfo | null = analysisResult.patientInfo\n    ? JSON.parse(analysisResult.patientInfo)\n    : null\n\n  const getStatusColor = (status: string) => {\n    switch (status.toLowerCase()) {\n      case \"completed\":\n        return \"default\"\n      case \"processing\":\n        return \"secondary\"\n      case \"failed\":\n        return \"destructive\"\n      default:\n        return \"outline\"\n    }\n  }\n\n  const getStatusIcon = (status: string) => {\n    switch (status.toLowerCase()) {\n      case \"completed\":\n        return <CheckCircle2 className=\"w-4 h-4\" />\n      case \"processing\":\n        return <Clock className=\"w-4 h-4 animate-pulse\" />\n      case \"failed\":\n        return <AlertTriangle className=\"w-4 h-4\" />\n      default:\n        return <Clock className=\"w-4 h-4\" />\n    }\n  }\n\n  const getUrgencyColor = (urgency: string) => {\n    switch (urgency.toLowerCase()) {\n      case \"high\":\n      case \"critical\":\n        return \"destructive\"\n      case \"medium\":\n        return \"default\"\n      case \"low\":\n      default:\n        return \"secondary\"\n    }\n  }\n\n  const formatConfidence = (confidence?: number) => {\n    if (!confidence) return \"N/A\"\n    return `${Math.round(confidence * 100)}%`\n  }\n\n  const formatProcessingTime = (time?: number) => {\n    if (!time) return \"N/A\"\n    return `${(time / 1000).toFixed(1)}s`\n  }\n\n  if (analysisResult.status === \"FAILED\") {\n    return (\n      <Card className=\"border-red-200 dark:border-red-800\">\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2 text-red-600 dark:text-red-400\">\n            <AlertTriangle className=\"w-5 h-5\" />\n            <span>Analysis Failed</span>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <p className=\"text-red-600 dark:text-red-400 mb-4\">\n            {analysisResult.errorMessage || \"An error occurred during analysis\"}\n          </p>\n          {showActions && (\n            <div className=\"flex space-x-2\">\n              {onViewDocument && (\n                <Button variant=\"outline\" size=\"sm\" onClick={onViewDocument}>\n                  <Eye className=\"w-4 h-4 mr-2\" />\n                  View Document\n                </Button>\n              )}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <Card>\n        <CardHeader>\n          <div className=\"flex items-center justify-between\">\n            <CardTitle className=\"flex items-center space-x-2\">\n              <Brain className=\"w-5 h-5 text-blue-600\" />\n              <span>AI Analysis Results</span>\n            </CardTitle>\n            <div className=\"flex items-center space-x-2\">\n              <Badge variant={getStatusColor(analysisResult.status)} className=\"flex items-center space-x-1\">\n                {getStatusIcon(analysisResult.status)}\n                <span>{analysisResult.status}</span>\n              </Badge>\n              {showActions && (\n                <div className=\"flex space-x-1\">\n                  {onViewDocument && (\n                    <Button variant=\"ghost\" size=\"sm\" onClick={onViewDocument}>\n                      <Eye className=\"w-4 h-4\" />\n                    </Button>\n                  )}\n                  {onDownloadReport && (\n                    <Button variant=\"ghost\" size=\"sm\" onClick={onDownloadReport}>\n                      <Download className=\"w-4 h-4\" />\n                    </Button>\n                  )}\n                  {onShareResults && (\n                    <Button variant=\"ghost\" size=\"sm\" onClick={onShareResults}>\n                      <Share className=\"w-4 h-4\" />\n                    </Button>\n                  )}\n                </div>\n              )}\n            </div>\n          </div>\n        </CardHeader>\n        <CardContent>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n            <div>\n              <span className=\"text-gray-500 dark:text-gray-400\">Document Type:</span>\n              <p className=\"font-medium\">{parsedData?.document_type || \"Unknown\"}</p>\n            </div>\n            <div>\n              <span className=\"text-gray-500 dark:text-gray-400\">Confidence:</span>\n              <p className=\"font-medium\">{formatConfidence(analysisResult.confidence)}</p>\n            </div>\n            <div>\n              <span className=\"text-gray-500 dark:text-gray-400\">Processing Time:</span>\n              <p className=\"font-medium\">{formatProcessingTime(analysisResult.processingTime)}</p>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Analysis Content */}\n      <Tabs defaultValue=\"overview\" className=\"w-full\">\n        <TabsList className=\"grid w-full grid-cols-4\">\n          <TabsTrigger value=\"overview\">Overview</TabsTrigger>\n          <TabsTrigger value=\"medical\">Medical Data</TabsTrigger>\n          <TabsTrigger value=\"recommendations\">Recommendations</TabsTrigger>\n          <TabsTrigger value=\"raw\">Raw Data</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"overview\" className=\"space-y-4\">\n          {/* Patient Information */}\n          {parsedPatientInfo && (\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center space-x-2\">\n                  <User className=\"w-5 h-5 text-green-600\" />\n                  <span>Patient Information</span>\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  {parsedPatientInfo.name && (\n                    <div>\n                      <span className=\"text-sm text-gray-500 dark:text-gray-400\">Name:</span>\n                      <p className=\"font-medium\">{parsedPatientInfo.name}</p>\n                    </div>\n                  )}\n                  {parsedPatientInfo.age && (\n                    <div>\n                      <span className=\"text-sm text-gray-500 dark:text-gray-400\">Age:</span>\n                      <p className=\"font-medium\">{parsedPatientInfo.age} years</p>\n                    </div>\n                  )}\n                  {parsedPatientInfo.gender && (\n                    <div>\n                      <span className=\"text-sm text-gray-500 dark:text-gray-400\">Gender:</span>\n                      <p className=\"font-medium\">{parsedPatientInfo.gender}</p>\n                    </div>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* Urgency Level */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <AlertTriangle className=\"w-5 h-5 text-orange-600\" />\n                <span>Urgency Assessment</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"flex items-center space-x-4\">\n                <Badge variant={getUrgencyColor(analysisResult.urgencyLevel)} className=\"text-lg px-4 py-2\">\n                  {analysisResult.urgencyLevel} Priority\n                </Badge>\n                {analysisResult.routingReason && (\n                  <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                    {analysisResult.routingReason}\n                  </p>\n                )}\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n\n        <TabsContent value=\"medical\" className=\"space-y-4\">\n          {/* Symptoms */}\n          {analysisResult.symptoms.length > 0 && (\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center space-x-2\">\n                  <Activity className=\"w-5 h-5 text-red-600\" />\n                  <span>Symptoms ({analysisResult.symptoms.length})</span>\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"flex flex-wrap gap-2\">\n                  {analysisResult.symptoms.map((symptom, index) => (\n                    <Badge key={index} variant=\"outline\">\n                      {symptom}\n                    </Badge>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* Medications */}\n          {analysisResult.medications.length > 0 && (\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center space-x-2\">\n                  <Pill className=\"w-5 h-5 text-blue-600\" />\n                  <span>Medications ({analysisResult.medications.length})</span>\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-2\">\n                  {analysisResult.medications.map((medication, index) => (\n                    <div key={index} className=\"p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n                      <p className=\"text-sm font-medium\">{medication}</p>\n                    </div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* Diagnoses */}\n          {analysisResult.diagnoses.length > 0 && (\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center space-x-2\">\n                  <FileText className=\"w-5 h-5 text-purple-600\" />\n                  <span>Diagnoses & Conditions ({analysisResult.diagnoses.length})</span>\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"flex flex-wrap gap-2\">\n                  {analysisResult.diagnoses.map((diagnosis, index) => (\n                    <Badge key={index} variant=\"secondary\">\n                      {diagnosis}\n                    </Badge>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </TabsContent>\n\n        <TabsContent value=\"recommendations\" className=\"space-y-4\">\n          {/* Suggested Tests */}\n          {analysisResult.suggestedTests.length > 0 && (\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center space-x-2\">\n                  <TestTube className=\"w-5 h-5 text-green-600\" />\n                  <span>Suggested Tests ({analysisResult.suggestedTests.length})</span>\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-2\">\n                  {analysisResult.suggestedTests.map((test, index) => (\n                    <div key={index} className=\"flex items-center space-x-2 p-2 border rounded-lg\">\n                      <TestTube className=\"w-4 h-4 text-green-600\" />\n                      <span className=\"text-sm\">{test}</span>\n                    </div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {/* Recommended Specialists */}\n          {analysisResult.recommendedSpecialists.length > 0 && (\n            <Card>\n              <CardHeader>\n                <CardTitle className=\"flex items-center space-x-2\">\n                  <Stethoscope className=\"w-5 h-5 text-blue-600\" />\n                  <span>Recommended Specialists ({analysisResult.recommendedSpecialists.length})</span>\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-2\">\n                  {analysisResult.recommendedSpecialists.map((specialist, index) => (\n                    <div key={index} className=\"flex items-center space-x-2 p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n                      <Stethoscope className=\"w-4 h-4 text-blue-600\" />\n                      <span className=\"text-sm font-medium\">{specialist}</span>\n                    </div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          )}\n        </TabsContent>\n\n        <TabsContent value=\"raw\" className=\"space-y-4\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Raw Analysis Data</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <pre className=\"text-xs bg-gray-100 dark:bg-gray-800 p-4 rounded-lg overflow-auto max-h-96\">\n                {JSON.stringify(parsedData, null, 2)}\n              </pre>\n            </CardContent>\n          </Card>\n        </TabsContent>\n      </Tabs>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;AAoCO,SAAS,gBAAgB,EAC9B,cAAc,EACd,cAAc,EACd,gBAAgB,EAChB,cAAc,EACd,cAAc,IAAI,EACG;;IACrB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI,IAAI;QAAC;KAAW;IAE1F,MAAM,gBAAgB,CAAC;QACrB,MAAM,cAAc,IAAI,IAAI;QAC5B,IAAI,YAAY,GAAG,CAAC,UAAU;YAC5B,YAAY,MAAM,CAAC;QACrB,OAAO;YACL,YAAY,GAAG,CAAC;QAClB;QACA,oBAAoB;IACtB;IAEA,MAAM,aAA4C,eAAe,aAAa,GAC1E,KAAK,KAAK,CAAC,eAAe,aAAa,IACvC;IAEJ,MAAM,oBAAwC,eAAe,WAAW,GACpE,KAAK,KAAK,CAAC,eAAe,WAAW,IACrC;IAEJ,MAAM,iBAAiB,CAAC;QACtB,OAAQ,OAAO,WAAW;YACxB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ,OAAO,WAAW;YACxB,KAAK;gBACH,qBAAO,6LAAC,wNAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YACjC,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ,QAAQ,WAAW;YACzB,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,YAAY,OAAO;QACxB,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,KAAK,CAAC,CAAC;IAC3C;IAEA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,GAAG,CAAC,OAAO,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;IACvC;IAEA,IAAI,eAAe,MAAM,KAAK,UAAU;QACtC,qBACE,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,2NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;0CACzB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;8BAGV,6LAAC,mIAAA,CAAA,cAAW;;sCACV,6LAAC;4BAAE,WAAU;sCACV,eAAe,YAAY,IAAI;;;;;;wBAEjC,6BACC,6LAAC;4BAAI,WAAU;sCACZ,gCACC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,SAAS;;kDAC3C,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;IAShD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;sDAAK;;;;;;;;;;;;8CAER,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAS,eAAe,eAAe,MAAM;4CAAG,WAAU;;gDAC9D,cAAc,eAAe,MAAM;8DACpC,6LAAC;8DAAM,eAAe,MAAM;;;;;;;;;;;;wCAE7B,6BACC,6LAAC;4CAAI,WAAU;;gDACZ,gCACC,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAK,SAAS;8DACzC,cAAA,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;gDAGlB,kCACC,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAK,SAAS;8DACzC,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;gDAGvB,gCACC,6LAAC,qIAAA,CAAA,SAAM;oDAAC,SAAQ;oDAAQ,MAAK;oDAAK,SAAS;8DACzC,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/B,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAAmC;;;;;;sDACnD,6LAAC;4CAAE,WAAU;sDAAe,YAAY,iBAAiB;;;;;;;;;;;;8CAE3D,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAAmC;;;;;;sDACnD,6LAAC;4CAAE,WAAU;sDAAe,iBAAiB,eAAe,UAAU;;;;;;;;;;;;8CAExE,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAAmC;;;;;;sDACnD,6LAAC;4CAAE,WAAU;sDAAe,qBAAqB,eAAe,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOtF,6LAAC,mIAAA,CAAA,OAAI;gBAAC,cAAa;gBAAW,WAAU;;kCACtC,6LAAC,mIAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAW;;;;;;0CAC9B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAU;;;;;;0CAC7B,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAkB;;;;;;0CACrC,6LAAC,mIAAA,CAAA,cAAW;gCAAC,OAAM;0CAAM;;;;;;;;;;;;kCAG3B,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAW,WAAU;;4BAErC,mCACC,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;kDAGV,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;gDACZ,kBAAkB,IAAI,kBACrB,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAA2C;;;;;;sEAC3D,6LAAC;4DAAE,WAAU;sEAAe,kBAAkB,IAAI;;;;;;;;;;;;gDAGrD,kBAAkB,GAAG,kBACpB,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAA2C;;;;;;sEAC3D,6LAAC;4DAAE,WAAU;;gEAAe,kBAAkB,GAAG;gEAAC;;;;;;;;;;;;;gDAGrD,kBAAkB,MAAM,kBACvB,6LAAC;;sEACC,6LAAC;4DAAK,WAAU;sEAA2C;;;;;;sEAC3D,6LAAC;4DAAE,WAAU;sEAAe,kBAAkB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAShE,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;kDAGV,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,oIAAA,CAAA,QAAK;oDAAC,SAAS,gBAAgB,eAAe,YAAY;oDAAG,WAAU;;wDACrE,eAAe,YAAY;wDAAC;;;;;;;gDAE9B,eAAe,aAAa,kBAC3B,6LAAC;oDAAE,WAAU;8DACV,eAAe,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQzC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAU,WAAU;;4BAEpC,eAAe,QAAQ,CAAC,MAAM,GAAG,mBAChC,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,6MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;;wDAAK;wDAAW,eAAe,QAAQ,CAAC,MAAM;wDAAC;;;;;;;;;;;;;;;;;;kDAGpD,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;sDACZ,eAAe,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACrC,6LAAC,oIAAA,CAAA,QAAK;oDAAa,SAAQ;8DACxB;mDADS;;;;;;;;;;;;;;;;;;;;;4BAUrB,eAAe,WAAW,CAAC,MAAM,GAAG,mBACnC,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC;;wDAAK;wDAAc,eAAe,WAAW,CAAC,MAAM;wDAAC;;;;;;;;;;;;;;;;;;kDAG1D,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;sDACZ,eAAe,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,sBAC3C,6LAAC;oDAAgB,WAAU;8DACzB,cAAA,6LAAC;wDAAE,WAAU;kEAAuB;;;;;;mDAD5B;;;;;;;;;;;;;;;;;;;;;4BAUnB,eAAe,SAAS,CAAC,MAAM,GAAG,mBACjC,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;;wDAAK;wDAAyB,eAAe,SAAS,CAAC,MAAM;wDAAC;;;;;;;;;;;;;;;;;;kDAGnE,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;sDACZ,eAAe,SAAS,CAAC,GAAG,CAAC,CAAC,WAAW,sBACxC,6LAAC,oIAAA,CAAA,QAAK;oDAAa,SAAQ;8DACxB;mDADS;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUxB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAkB,WAAU;;4BAE5C,eAAe,cAAc,CAAC,MAAM,GAAG,mBACtC,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;;wDAAK;wDAAkB,eAAe,cAAc,CAAC,MAAM;wDAAC;;;;;;;;;;;;;;;;;;kDAGjE,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;sDACZ,eAAe,cAAc,CAAC,GAAG,CAAC,CAAC,MAAM,sBACxC,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC,iNAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,6LAAC;4DAAK,WAAU;sEAAW;;;;;;;mDAFnB;;;;;;;;;;;;;;;;;;;;;4BAWnB,eAAe,sBAAsB,CAAC,MAAM,GAAG,mBAC9C,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,mNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,6LAAC;;wDAAK;wDAA0B,eAAe,sBAAsB,CAAC,MAAM;wDAAC;;;;;;;;;;;;;;;;;;kDAGjF,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;sDACZ,eAAe,sBAAsB,CAAC,GAAG,CAAC,CAAC,YAAY,sBACtD,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC,mNAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,6LAAC;4DAAK,WAAU;sEAAuB;;;;;;;mDAF/B;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWtB,6LAAC,mIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAM,WAAU;kCACjC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;kDACZ,KAAK,SAAS,CAAC,YAAY,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlD;GAhWgB;KAAA", "debugId": null}}, {"offset": {"line": 2793, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/types/doctors.ts"], "sourcesContent": ["import { Doctor<PERSON><PERSON><PERSON><PERSON> } from \"@prisma/client\";\r\n\r\nexport type Doctor = {\r\n  id: string;\r\n  name: string;\r\n  email: string;\r\n  specialty?: DoctorSpec<PERSON><PERSON>;\r\n  licenseNumber?: string;\r\n  yearsOfExperience?: number;\r\n  bio?: string;\r\n  consultationFee?: number;\r\n};\r\n\r\n// Extended doctor type with additional computed fields\r\nexport type EnhancedDoctor = Doctor & {\r\n  rating?: number;\r\n  availableSlots?: Date[];\r\n  isAvailable?: boolean;\r\n  patientCount?: number;\r\n  nextAvailableSlot?: Date;\r\n};\r\n\r\n// Doctor specialty mapping for display\r\nexport const DoctorSpecialtyLabels: Record<DoctorSpecialty, string> = {\r\n  GENERAL_MEDICINE: \"General Medicine\",\r\n  CARDIOLOGY: \"Cardiology\",\r\n  NEUROLOGY: \"Neurology\",\r\n  ORTHOPEDICS: \"Orthopedics\",\r\n  DERMATOLOGY: \"Dermatology\",\r\n  PEDIATRICS: \"Pediatrics\",\r\n  GYNECOLOGY: \"Gynecology\",\r\n  PSYCHIATRY: \"Psychiatry\",\r\n  RADIOLOGY: \"Radiology\",\r\n  PATHOLOGY: \"Pathology\",\r\n  ONCOLOGY: \"Oncology\",\r\n  ENDOCRINOLOGY: \"Endocrinology\",\r\n  GASTROENTEROLOGY: \"Gastroenterology\",\r\n  PULMONOLOGY: \"Pulmonology\",\r\n  NEPHROLOGY: \"Nephrology\",\r\n  OPHTHALMOLOGY: \"Ophthalmology\",\r\n  ENT: \"ENT (Ear, Nose, Throat)\",\r\n  ANESTHESIOLOGY: \"Anesthesiology\",\r\n  EMERGENCY_MEDICINE: \"Emergency Medicine\",\r\n  FAMILY_MEDICINE: \"Family Medicine\",\r\n};\r\n\r\n// Specialty to condition mapping for smart routing\r\nexport const SpecialtyConditionMapping: Record<string, DoctorSpecialty[]> = {\r\n  // Cardiovascular conditions\r\n  heart: [DoctorSpecialty.CARDIOLOGY, DoctorSpecialty.GENERAL_MEDICINE],\r\n  cardiac: [DoctorSpecialty.CARDIOLOGY],\r\n  hypertension: [DoctorSpecialty.CARDIOLOGY, DoctorSpecialty.GENERAL_MEDICINE],\r\n  \"chest pain\": [\r\n    DoctorSpecialty.CARDIOLOGY,\r\n    DoctorSpecialty.EMERGENCY_MEDICINE,\r\n  ],\r\n\r\n  // Neurological conditions\r\n  headache: [DoctorSpecialty.NEUROLOGY, DoctorSpecialty.GENERAL_MEDICINE],\r\n  migraine: [DoctorSpecialty.NEUROLOGY],\r\n  seizure: [DoctorSpecialty.NEUROLOGY, DoctorSpecialty.EMERGENCY_MEDICINE],\r\n  stroke: [DoctorSpecialty.NEUROLOGY, DoctorSpecialty.EMERGENCY_MEDICINE],\r\n  dementia: [DoctorSpecialty.NEUROLOGY, DoctorSpecialty.PSYCHIATRY],\r\n\r\n  // Orthopedic conditions\r\n  fracture: [DoctorSpecialty.ORTHOPEDICS, DoctorSpecialty.EMERGENCY_MEDICINE],\r\n  \"joint pain\": [DoctorSpecialty.ORTHOPEDICS, DoctorSpecialty.GENERAL_MEDICINE],\r\n  arthritis: [DoctorSpecialty.ORTHOPEDICS],\r\n  \"back pain\": [DoctorSpecialty.ORTHOPEDICS, DoctorSpecialty.GENERAL_MEDICINE],\r\n\r\n  // Skin conditions\r\n  rash: [DoctorSpecialty.DERMATOLOGY, DoctorSpecialty.GENERAL_MEDICINE],\r\n  acne: [DoctorSpecialty.DERMATOLOGY],\r\n  eczema: [DoctorSpecialty.DERMATOLOGY],\r\n  psoriasis: [DoctorSpecialty.DERMATOLOGY],\r\n\r\n  // Respiratory conditions\r\n  cough: [DoctorSpecialty.PULMONOLOGY, DoctorSpecialty.GENERAL_MEDICINE],\r\n  asthma: [DoctorSpecialty.PULMONOLOGY],\r\n  pneumonia: [DoctorSpecialty.PULMONOLOGY, DoctorSpecialty.GENERAL_MEDICINE],\r\n  \"shortness of breath\": [\r\n    DoctorSpecialty.PULMONOLOGY,\r\n    DoctorSpecialty.CARDIOLOGY,\r\n  ],\r\n\r\n  // Digestive conditions\r\n  \"stomach pain\": [\r\n    DoctorSpecialty.GASTROENTEROLOGY,\r\n    DoctorSpecialty.GENERAL_MEDICINE,\r\n  ],\r\n  nausea: [DoctorSpecialty.GASTROENTEROLOGY, DoctorSpecialty.GENERAL_MEDICINE],\r\n  diarrhea: [\r\n    DoctorSpecialty.GASTROENTEROLOGY,\r\n    DoctorSpecialty.GENERAL_MEDICINE,\r\n  ],\r\n  constipation: [\r\n    DoctorSpecialty.GASTROENTEROLOGY,\r\n    DoctorSpecialty.GENERAL_MEDICINE,\r\n  ],\r\n\r\n  // Mental health conditions\r\n  depression: [DoctorSpecialty.PSYCHIATRY, DoctorSpecialty.GENERAL_MEDICINE],\r\n  anxiety: [DoctorSpecialty.PSYCHIATRY, DoctorSpecialty.GENERAL_MEDICINE],\r\n  insomnia: [DoctorSpecialty.PSYCHIATRY, DoctorSpecialty.GENERAL_MEDICINE],\r\n\r\n  // Endocrine conditions\r\n  diabetes: [DoctorSpecialty.ENDOCRINOLOGY, DoctorSpecialty.GENERAL_MEDICINE],\r\n  thyroid: [DoctorSpecialty.ENDOCRINOLOGY],\r\n  hormone: [DoctorSpecialty.ENDOCRINOLOGY, DoctorSpecialty.GYNECOLOGY],\r\n\r\n  // Eye conditions\r\n  vision: [DoctorSpecialty.OPHTHALMOLOGY],\r\n  \"eye pain\": [DoctorSpecialty.OPHTHALMOLOGY],\r\n  \"blurred vision\": [DoctorSpecialty.OPHTHALMOLOGY, DoctorSpecialty.NEUROLOGY],\r\n\r\n  // General conditions\r\n  fever: [DoctorSpecialty.GENERAL_MEDICINE, DoctorSpecialty.EMERGENCY_MEDICINE],\r\n  fatigue: [DoctorSpecialty.GENERAL_MEDICINE],\r\n  \"weight loss\": [DoctorSpecialty.GENERAL_MEDICINE, DoctorSpecialty.ONCOLOGY],\r\n  \"weight gain\": [\r\n    DoctorSpecialty.GENERAL_MEDICINE,\r\n    DoctorSpecialty.ENDOCRINOLOGY,\r\n  ],\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;;AAuBO,MAAM,wBAAyD;IACpE,kBAAkB;IAClB,YAAY;IACZ,WAAW;IACX,aAAa;IACb,aAAa;IACb,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,WAAW;IACX,WAAW;IACX,UAAU;IACV,eAAe;IACf,kBAAkB;IAClB,aAAa;IACb,YAAY;IACZ,eAAe;IACf,KAAK;IACL,gBAAgB;IAChB,oBAAoB;IACpB,iBAAiB;AACnB;AAGO,MAAM,4BAA+D;IAC1E,4BAA4B;IAC5B,OAAO;QAAC,yJAAA,CAAA,kBAAe,CAAC,UAAU;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IACrE,SAAS;QAAC,yJAAA,CAAA,kBAAe,CAAC,UAAU;KAAC;IACrC,cAAc;QAAC,yJAAA,CAAA,kBAAe,CAAC,UAAU;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IAC5E,cAAc;QACZ,yJAAA,CAAA,kBAAe,CAAC,UAAU;QAC1B,yJAAA,CAAA,kBAAe,CAAC,kBAAkB;KACnC;IAED,0BAA0B;IAC1B,UAAU;QAAC,yJAAA,CAAA,kBAAe,CAAC,SAAS;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IACvE,UAAU;QAAC,yJAAA,CAAA,kBAAe,CAAC,SAAS;KAAC;IACrC,SAAS;QAAC,yJAAA,CAAA,kBAAe,CAAC,SAAS;QAAE,yJAAA,CAAA,kBAAe,CAAC,kBAAkB;KAAC;IACxE,QAAQ;QAAC,yJAAA,CAAA,kBAAe,CAAC,SAAS;QAAE,yJAAA,CAAA,kBAAe,CAAC,kBAAkB;KAAC;IACvE,UAAU;QAAC,yJAAA,CAAA,kBAAe,CAAC,SAAS;QAAE,yJAAA,CAAA,kBAAe,CAAC,UAAU;KAAC;IAEjE,wBAAwB;IACxB,UAAU;QAAC,yJAAA,CAAA,kBAAe,CAAC,WAAW;QAAE,yJAAA,CAAA,kBAAe,CAAC,kBAAkB;KAAC;IAC3E,cAAc;QAAC,yJAAA,CAAA,kBAAe,CAAC,WAAW;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IAC7E,WAAW;QAAC,yJAAA,CAAA,kBAAe,CAAC,WAAW;KAAC;IACxC,aAAa;QAAC,yJAAA,CAAA,kBAAe,CAAC,WAAW;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IAE5E,kBAAkB;IAClB,MAAM;QAAC,yJAAA,CAAA,kBAAe,CAAC,WAAW;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IACrE,MAAM;QAAC,yJAAA,CAAA,kBAAe,CAAC,WAAW;KAAC;IACnC,QAAQ;QAAC,yJAAA,CAAA,kBAAe,CAAC,WAAW;KAAC;IACrC,WAAW;QAAC,yJAAA,CAAA,kBAAe,CAAC,WAAW;KAAC;IAExC,yBAAyB;IACzB,OAAO;QAAC,yJAAA,CAAA,kBAAe,CAAC,WAAW;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IACtE,QAAQ;QAAC,yJAAA,CAAA,kBAAe,CAAC,WAAW;KAAC;IACrC,WAAW;QAAC,yJAAA,CAAA,kBAAe,CAAC,WAAW;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IAC1E,uBAAuB;QACrB,yJAAA,CAAA,kBAAe,CAAC,WAAW;QAC3B,yJAAA,CAAA,kBAAe,CAAC,UAAU;KAC3B;IAED,uBAAuB;IACvB,gBAAgB;QACd,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;QAChC,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KACjC;IACD,QAAQ;QAAC,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IAC5E,UAAU;QACR,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;QAChC,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KACjC;IACD,cAAc;QACZ,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;QAChC,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KACjC;IAED,2BAA2B;IAC3B,YAAY;QAAC,yJAAA,CAAA,kBAAe,CAAC,UAAU;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IAC1E,SAAS;QAAC,yJAAA,CAAA,kBAAe,CAAC,UAAU;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IACvE,UAAU;QAAC,yJAAA,CAAA,kBAAe,CAAC,UAAU;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IAExE,uBAAuB;IACvB,UAAU;QAAC,yJAAA,CAAA,kBAAe,CAAC,aAAa;QAAE,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IAC3E,SAAS;QAAC,yJAAA,CAAA,kBAAe,CAAC,aAAa;KAAC;IACxC,SAAS;QAAC,yJAAA,CAAA,kBAAe,CAAC,aAAa;QAAE,yJAAA,CAAA,kBAAe,CAAC,UAAU;KAAC;IAEpE,iBAAiB;IACjB,QAAQ;QAAC,yJAAA,CAAA,kBAAe,CAAC,aAAa;KAAC;IACvC,YAAY;QAAC,yJAAA,CAAA,kBAAe,CAAC,aAAa;KAAC;IAC3C,kBAAkB;QAAC,yJAAA,CAAA,kBAAe,CAAC,aAAa;QAAE,yJAAA,CAAA,kBAAe,CAAC,SAAS;KAAC;IAE5E,qBAAqB;IACrB,OAAO;QAAC,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;QAAE,yJAAA,CAAA,kBAAe,CAAC,kBAAkB;KAAC;IAC7E,SAAS;QAAC,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;KAAC;IAC3C,eAAe;QAAC,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;QAAE,yJAAA,CAAA,kBAAe,CAAC,QAAQ;KAAC;IAC3E,eAAe;QACb,yJAAA,CAAA,kBAAe,CAAC,gBAAgB;QAChC,yJAAA,CAAA,kBAAe,CAAC,aAAa;KAC9B;AACH", "debugId": null}}, {"offset": {"line": 2983, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/medical/smart-routing.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useState, useEffect } from \"react\"\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\"\nimport { <PERSON>ge } from \"@/components/ui/badge\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\"\nimport { Separator } from \"@/components/ui/separator\"\nimport { \n  Brain, \n  Clock, \n  AlertTriangle, \n  User, \n  Star,\n  Calendar,\n  DollarSign,\n  ArrowRight,\n  Stethoscope,\n  FileText,\n  Activity\n} from \"lucide-react\"\nimport { RoutingRecommendation, MedicalContext } from \"@/types/medical-documents\"\nimport { DoctorSpecialtyLabels } from \"@/types/doctors\"\n\ninterface SmartRoutingProps {\n  recommendations: RoutingRecommendation[]\n  medicalContext: MedicalContext\n  onBookAppointment: (doctorId: string, context: MedicalContext) => void\n  isLoading?: boolean\n}\n\nexport function SmartRouting({\n  recommendations,\n  medicalContext,\n  onBookAppointment,\n  isLoading = false\n}: SmartRoutingProps) {\n  const [selectedDoctor, setSelectedDoctor] = useState<string | null>(null)\n\n  const getUrgencyColor = (urgency: string) => {\n    switch (urgency.toLowerCase()) {\n      case \"high\":\n      case \"critical\":\n        return \"destructive\"\n      case \"medium\":\n        return \"default\"\n      case \"low\":\n      default:\n        return \"secondary\"\n    }\n  }\n\n  const getUrgencyIcon = (urgency: string) => {\n    switch (urgency.toLowerCase()) {\n      case \"high\":\n      case \"critical\":\n        return <AlertTriangle className=\"w-4 h-4\" />\n      case \"medium\":\n        return <Clock className=\"w-4 h-4\" />\n      case \"low\":\n      default:\n        return <Activity className=\"w-4 h-4\" />\n    }\n  }\n\n  const formatMatchScore = (score: number) => {\n    return Math.round(score * 100)\n  }\n\n  if (isLoading) {\n    return (\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <Brain className=\"w-5 h-5 text-blue-600 animate-pulse\" />\n            <span>Analyzing Medical Context...</span>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"space-y-4\">\n            {[1, 2, 3].map((i) => (\n              <div key={i} className=\"animate-pulse\">\n                <div className=\"flex items-center space-x-4\">\n                  <div className=\"w-12 h-12 bg-gray-200 rounded-full\"></div>\n                  <div className=\"flex-1 space-y-2\">\n                    <div className=\"h-4 bg-gray-200 rounded w-3/4\"></div>\n                    <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Medical Context Summary */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <FileText className=\"w-5 h-5 text-blue-600\" />\n            <span>Medical Context Summary</span>\n          </CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <h4 className=\"font-medium text-sm text-gray-700 dark:text-gray-300 mb-2\">\n                Key Symptoms\n              </h4>\n              <div className=\"flex flex-wrap gap-1\">\n                {medicalContext.symptoms.slice(0, 5).map((symptom, index) => (\n                  <Badge key={index} variant=\"outline\" className=\"text-xs\">\n                    {symptom}\n                  </Badge>\n                ))}\n                {medicalContext.symptoms.length > 5 && (\n                  <Badge variant=\"outline\" className=\"text-xs\">\n                    +{medicalContext.symptoms.length - 5} more\n                  </Badge>\n                )}\n              </div>\n            </div>\n\n            <div>\n              <h4 className=\"font-medium text-sm text-gray-700 dark:text-gray-300 mb-2\">\n                Diagnoses\n              </h4>\n              <div className=\"flex flex-wrap gap-1\">\n                {medicalContext.diagnoses.slice(0, 3).map((diagnosis, index) => (\n                  <Badge key={index} variant=\"secondary\" className=\"text-xs\">\n                    {diagnosis}\n                  </Badge>\n                ))}\n                {medicalContext.diagnoses.length > 3 && (\n                  <Badge variant=\"secondary\" className=\"text-xs\">\n                    +{medicalContext.diagnoses.length - 3} more\n                  </Badge>\n                )}\n              </div>\n            </div>\n          </div>\n\n          <div className=\"flex items-center justify-between pt-2\">\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">\n                Urgency Level:\n              </span>\n              <Badge variant={getUrgencyColor(medicalContext.urgencyLevel)} className=\"flex items-center space-x-1\">\n                {getUrgencyIcon(medicalContext.urgencyLevel)}\n                <span>{medicalContext.urgencyLevel}</span>\n              </Badge>\n            </div>\n            <div className=\"text-sm text-gray-500 dark:text-gray-400\">\n              Based on {medicalContext.documentIds.length} document(s)\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Doctor Recommendations */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <Stethoscope className=\"w-5 h-5 text-green-600\" />\n            <span>Recommended Specialists</span>\n          </CardTitle>\n          <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n            AI-powered recommendations based on your medical documents\n          </p>\n        </CardHeader>\n        <CardContent>\n          {recommendations.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <User className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n              <p className=\"text-gray-500 dark:text-gray-400\">\n                No specialist recommendations available at this time.\n              </p>\n            </div>\n          ) : (\n            <div className=\"space-y-4\">\n              {recommendations.map((recommendation) => (\n                <Card \n                  key={recommendation.doctorId}\n                  className={`cursor-pointer transition-all duration-200 ${\n                    selectedDoctor === recommendation.doctorId\n                      ? \"ring-2 ring-blue-500 border-blue-500\"\n                      : \"hover:shadow-md\"\n                  }`}\n                  onClick={() => setSelectedDoctor(recommendation.doctorId)}\n                >\n                  <CardContent className=\"p-4\">\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex items-start space-x-4 flex-1\">\n                        <Avatar className=\"w-12 h-12\">\n                          <AvatarImage src={`/api/avatar/${recommendation.doctor.id}`} />\n                          <AvatarFallback>\n                            {recommendation.doctor.name?.split(' ').map(n => n[0]).join('') || 'DR'}\n                          </AvatarFallback>\n                        </Avatar>\n\n                        <div className=\"flex-1 min-w-0\">\n                          <div className=\"flex items-center space-x-2 mb-1\">\n                            <h3 className=\"font-semibold text-gray-900 dark:text-white\">\n                              Dr. {recommendation.doctor.name}\n                            </h3>\n                            <Badge variant=\"outline\" className=\"text-xs\">\n                              {formatMatchScore(recommendation.matchScore)}% match\n                            </Badge>\n                          </div>\n\n                          <div className=\"space-y-2\">\n                            {recommendation.doctor.specialty && (\n                              <div className=\"flex items-center space-x-2\">\n                                <Badge variant=\"secondary\" className=\"text-xs\">\n                                  {DoctorSpecialtyLabels[recommendation.doctor.specialty]}\n                                </Badge>\n                                {recommendation.doctor.yearsOfExperience && (\n                                  <span className=\"text-xs text-gray-500\">\n                                    {recommendation.doctor.yearsOfExperience} years exp.\n                                  </span>\n                                )}\n                              </div>\n                            )}\n\n                            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                              {recommendation.reason}\n                            </p>\n\n                            <div className=\"flex items-center space-x-4 text-xs text-gray-500\">\n                              {recommendation.doctor.consultationFee && (\n                                <div className=\"flex items-center space-x-1\">\n                                  <DollarSign className=\"w-3 h-3\" />\n                                  <span>${recommendation.doctor.consultationFee}</span>\n                                </div>\n                              )}\n                              {recommendation.availableSlots && recommendation.availableSlots.length > 0 && (\n                                <div className=\"flex items-center space-x-1\">\n                                  <Calendar className=\"w-3 h-3\" />\n                                  <span>Next: {new Date(recommendation.availableSlots[0]).toLocaleDateString()}</span>\n                                </div>\n                              )}\n                            </div>\n                          </div>\n                        </div>\n                      </div>\n\n                      <div className=\"flex flex-col items-end space-y-2\">\n                        <Badge \n                          variant={getUrgencyColor(recommendation.urgencyLevel)}\n                          className=\"text-xs\"\n                        >\n                          {recommendation.urgencyLevel} Priority\n                        </Badge>\n\n                        <Button\n                          size=\"sm\"\n                          onClick={(e) => {\n                            e.stopPropagation()\n                            onBookAppointment(recommendation.doctorId, medicalContext)\n                          }}\n                          className=\"flex items-center space-x-1\"\n                        >\n                          <Calendar className=\"w-3 h-3\" />\n                          <span>Book</span>\n                          <ArrowRight className=\"w-3 h-3\" />\n                        </Button>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Key Findings */}\n      {medicalContext.keyFindings.length > 0 && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <Brain className=\"w-5 h-5 text-purple-600\" />\n              <span>Key Medical Findings</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-3\">\n              {medicalContext.keyFindings.map((finding, index) => (\n                <div key={index} className=\"flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg\">\n                  <div className=\"w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0\"></div>\n                  <p className=\"text-sm text-gray-700 dark:text-gray-300\">{finding}</p>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Recommended Actions */}\n      {medicalContext.recommendedActions.length > 0 && (\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <Activity className=\"w-5 h-5 text-orange-600\" />\n              <span>Recommended Actions</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-3\">\n              {medicalContext.recommendedActions.map((action, index) => (\n                <div key={index} className=\"flex items-start space-x-3 p-3 border-l-4 border-orange-500 bg-orange-50 dark:bg-orange-900/20\">\n                  <ArrowRight className=\"w-4 h-4 text-orange-600 mt-0.5 flex-shrink-0\" />\n                  <p className=\"text-sm text-gray-700 dark:text-gray-300\">{action}</p>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAcA;;;AAtBA;;;;;;;;AA+BO,SAAS,aAAa,EAC3B,eAAe,EACf,cAAc,EACd,iBAAiB,EACjB,YAAY,KAAK,EACC;;IAClB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,MAAM,kBAAkB,CAAC;QACvB,OAAQ,QAAQ,WAAW;YACzB,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ,QAAQ,WAAW;YACzB,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;YACL;gBACE,qBAAO,6LAAC,6MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;QAC/B;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAO,KAAK,KAAK,CAAC,QAAQ;IAC5B;IAEA,IAAI,WAAW;QACb,qBACE,6LAAC,mIAAA,CAAA,OAAI;;8BACH,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,uMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;8BAGV,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,kBACd,6LAAC;gCAAY,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;+BALX;;;;;;;;;;;;;;;;;;;;;IActB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,iNAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;kCAGV,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAI,WAAU;;oDACZ,eAAe,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBACjD,6LAAC,oIAAA,CAAA,QAAK;4DAAa,SAAQ;4DAAU,WAAU;sEAC5C;2DADS;;;;;oDAIb,eAAe,QAAQ,CAAC,MAAM,GAAG,mBAChC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;;4DAAU;4DACzC,eAAe,QAAQ,CAAC,MAAM,GAAG;4DAAE;;;;;;;;;;;;;;;;;;;kDAM7C,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,6LAAC;gDAAI,WAAU;;oDACZ,eAAe,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,WAAW,sBACpD,6LAAC,oIAAA,CAAA,QAAK;4DAAa,SAAQ;4DAAY,WAAU;sEAC9C;2DADS;;;;;oDAIb,eAAe,SAAS,CAAC,MAAM,GAAG,mBACjC,6LAAC,oIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAY,WAAU;;4DAAU;4DAC3C,eAAe,SAAS,CAAC,MAAM,GAAG;4DAAE;;;;;;;;;;;;;;;;;;;;;;;;;0CAOhD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;0DAG3D,6LAAC,oIAAA,CAAA,QAAK;gDAAC,SAAS,gBAAgB,eAAe,YAAY;gDAAG,WAAU;;oDACrE,eAAe,eAAe,YAAY;kEAC3C,6LAAC;kEAAM,eAAe,YAAY;;;;;;;;;;;;;;;;;;kDAGtC,6LAAC;wCAAI,WAAU;;4CAA2C;4CAC9C,eAAe,WAAW,CAAC,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;0BAOpD,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,mNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;kCAI1D,6LAAC,mIAAA,CAAA,cAAW;kCACT,gBAAgB,MAAM,KAAK,kBAC1B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,6LAAC;oCAAE,WAAU;8CAAmC;;;;;;;;;;;iDAKlD,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,+BACpB,6LAAC,mIAAA,CAAA,OAAI;oCAEH,WAAW,CAAC,2CAA2C,EACrD,mBAAmB,eAAe,QAAQ,GACtC,yCACA,mBACJ;oCACF,SAAS,IAAM,kBAAkB,eAAe,QAAQ;8CAExD,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DAAC,WAAU;;8EAChB,6LAAC,qIAAA,CAAA,cAAW;oEAAC,KAAK,CAAC,YAAY,EAAE,eAAe,MAAM,CAAC,EAAE,EAAE;;;;;;8EAC3D,6LAAC,qIAAA,CAAA,iBAAc;8EACZ,eAAe,MAAM,CAAC,IAAI,EAAE,MAAM,KAAK,IAAI,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,KAAK,OAAO;;;;;;;;;;;;sEAIvE,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;;gFAA8C;gFACrD,eAAe,MAAM,CAAC,IAAI;;;;;;;sFAEjC,6LAAC,oIAAA,CAAA,QAAK;4EAAC,SAAQ;4EAAU,WAAU;;gFAChC,iBAAiB,eAAe,UAAU;gFAAE;;;;;;;;;;;;;8EAIjD,6LAAC;oEAAI,WAAU;;wEACZ,eAAe,MAAM,CAAC,SAAS,kBAC9B,6LAAC;4EAAI,WAAU;;8FACb,6LAAC,oIAAA,CAAA,QAAK;oFAAC,SAAQ;oFAAY,WAAU;8FAClC,0HAAA,CAAA,wBAAqB,CAAC,eAAe,MAAM,CAAC,SAAS,CAAC;;;;;;gFAExD,eAAe,MAAM,CAAC,iBAAiB,kBACtC,6LAAC;oFAAK,WAAU;;wFACb,eAAe,MAAM,CAAC,iBAAiB;wFAAC;;;;;;;;;;;;;sFAMjD,6LAAC;4EAAE,WAAU;sFACV,eAAe,MAAM;;;;;;sFAGxB,6LAAC;4EAAI,WAAU;;gFACZ,eAAe,MAAM,CAAC,eAAe,kBACpC,6LAAC;oFAAI,WAAU;;sGACb,6LAAC,qNAAA,CAAA,aAAU;4FAAC,WAAU;;;;;;sGACtB,6LAAC;;gGAAK;gGAAE,eAAe,MAAM,CAAC,eAAe;;;;;;;;;;;;;gFAGhD,eAAe,cAAc,IAAI,eAAe,cAAc,CAAC,MAAM,GAAG,mBACvE,6LAAC;oFAAI,WAAU;;sGACb,6LAAC,6MAAA,CAAA,WAAQ;4FAAC,WAAU;;;;;;sGACpB,6LAAC;;gGAAK;gGAAO,IAAI,KAAK,eAAe,cAAc,CAAC,EAAE,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAQtF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oIAAA,CAAA,QAAK;4DACJ,SAAS,gBAAgB,eAAe,YAAY;4DACpD,WAAU;;gEAET,eAAe,YAAY;gEAAC;;;;;;;sEAG/B,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAS,CAAC;gEACR,EAAE,eAAe;gEACjB,kBAAkB,eAAe,QAAQ,EAAE;4DAC7C;4DACA,WAAU;;8EAEV,6LAAC,6MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;8EACpB,6LAAC;8EAAK;;;;;;8EACN,6LAAC,qNAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAlFzB,eAAe,QAAQ;;;;;;;;;;;;;;;;;;;;;YA+FvC,eAAe,WAAW,CAAC,MAAM,GAAG,mBACnC,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;kCAGV,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACZ,eAAe,WAAW,CAAC,GAAG,CAAC,CAAC,SAAS,sBACxC,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAE,WAAU;sDAA4C;;;;;;;mCAFjD;;;;;;;;;;;;;;;;;;;;;YAWnB,eAAe,kBAAkB,CAAC,MAAM,GAAG,mBAC1C,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;kCAGV,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACZ,eAAe,kBAAkB,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC9C,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;4CAAE,WAAU;sDAA4C;;;;;;;mCAFjD;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1B;GAtSgB;KAAA", "debugId": null}}, {"offset": {"line": 3882, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 3916, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 3947, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/ui/calendar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport {\r\n  ChevronDownIcon,\r\n  ChevronLeftIcon,\r\n  ChevronRightIcon,\r\n} from \"lucide-react\"\r\nimport { DayButton, DayPicker, getDefaultClassNames } from \"react-day-picker\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Button, buttonVariants } from \"@/components/ui/button\"\r\n\r\nfunction Calendar({\r\n  className,\r\n  classNames,\r\n  showOutsideDays = true,\r\n  captionLayout = \"label\",\r\n  buttonVariant = \"ghost\",\r\n  formatters,\r\n  components,\r\n  ...props\r\n}: React.ComponentProps<typeof DayPicker> & {\r\n  buttonVariant?: React.ComponentProps<typeof Button>[\"variant\"]\r\n}) {\r\n  const defaultClassNames = getDefaultClassNames()\r\n\r\n  return (\r\n    <DayPicker\r\n      showOutsideDays={showOutsideDays}\r\n      className={cn(\r\n        \"bg-background group/calendar p-3 [--cell-size:--spacing(8)] [[data-slot=card-content]_&]:bg-transparent [[data-slot=popover-content]_&]:bg-transparent\",\r\n        String.raw`rtl:**:[.rdp-button\\_next>svg]:rotate-180`,\r\n        String.raw`rtl:**:[.rdp-button\\_previous>svg]:rotate-180`,\r\n        className\r\n      )}\r\n      captionLayout={captionLayout}\r\n      formatters={{\r\n        formatMonthDropdown: (date) =>\r\n          date.toLocaleString(\"default\", { month: \"short\" }),\r\n        ...formatters,\r\n      }}\r\n      classNames={{\r\n        root: cn(\"w-fit\", defaultClassNames.root),\r\n        months: cn(\r\n          \"flex gap-4 flex-col md:flex-row relative\",\r\n          defaultClassNames.months\r\n        ),\r\n        month: cn(\"flex flex-col w-full gap-4\", defaultClassNames.month),\r\n        nav: cn(\r\n          \"flex items-center gap-1 w-full absolute top-0 inset-x-0 justify-between\",\r\n          defaultClassNames.nav\r\n        ),\r\n        button_previous: cn(\r\n          buttonVariants({ variant: buttonVariant }),\r\n          \"size-(--cell-size) aria-disabled:opacity-50 p-0 select-none\",\r\n          defaultClassNames.button_previous\r\n        ),\r\n        button_next: cn(\r\n          buttonVariants({ variant: buttonVariant }),\r\n          \"size-(--cell-size) aria-disabled:opacity-50 p-0 select-none\",\r\n          defaultClassNames.button_next\r\n        ),\r\n        month_caption: cn(\r\n          \"flex items-center justify-center h-(--cell-size) w-full px-(--cell-size)\",\r\n          defaultClassNames.month_caption\r\n        ),\r\n        dropdowns: cn(\r\n          \"w-full flex items-center text-sm font-medium justify-center h-(--cell-size) gap-1.5\",\r\n          defaultClassNames.dropdowns\r\n        ),\r\n        dropdown_root: cn(\r\n          \"relative has-focus:border-ring border border-input shadow-xs has-focus:ring-ring/50 has-focus:ring-[3px] rounded-md\",\r\n          defaultClassNames.dropdown_root\r\n        ),\r\n        dropdown: cn(\"absolute inset-0 opacity-0\", defaultClassNames.dropdown),\r\n        caption_label: cn(\r\n          \"select-none font-medium\",\r\n          captionLayout === \"label\"\r\n            ? \"text-sm\"\r\n            : \"rounded-md pl-2 pr-1 flex items-center gap-1 text-sm h-8 [&>svg]:text-muted-foreground [&>svg]:size-3.5\",\r\n          defaultClassNames.caption_label\r\n        ),\r\n        table: \"w-full border-collapse\",\r\n        weekdays: cn(\"flex\", defaultClassNames.weekdays),\r\n        weekday: cn(\r\n          \"text-muted-foreground rounded-md flex-1 font-normal text-[0.8rem] select-none\",\r\n          defaultClassNames.weekday\r\n        ),\r\n        week: cn(\"flex w-full mt-2\", defaultClassNames.week),\r\n        week_number_header: cn(\r\n          \"select-none w-(--cell-size)\",\r\n          defaultClassNames.week_number_header\r\n        ),\r\n        week_number: cn(\r\n          \"text-[0.8rem] select-none text-muted-foreground\",\r\n          defaultClassNames.week_number\r\n        ),\r\n        day: cn(\r\n          \"relative w-full h-full p-0 text-center [&:first-child[data-selected=true]_button]:rounded-l-md [&:last-child[data-selected=true]_button]:rounded-r-md group/day aspect-square select-none\",\r\n          defaultClassNames.day\r\n        ),\r\n        range_start: cn(\r\n          \"rounded-l-md bg-accent\",\r\n          defaultClassNames.range_start\r\n        ),\r\n        range_middle: cn(\"rounded-none\", defaultClassNames.range_middle),\r\n        range_end: cn(\"rounded-r-md bg-accent\", defaultClassNames.range_end),\r\n        today: cn(\r\n          \"bg-accent text-accent-foreground rounded-md data-[selected=true]:rounded-none\",\r\n          defaultClassNames.today\r\n        ),\r\n        outside: cn(\r\n          \"text-muted-foreground aria-selected:text-muted-foreground\",\r\n          defaultClassNames.outside\r\n        ),\r\n        disabled: cn(\r\n          \"text-muted-foreground opacity-50\",\r\n          defaultClassNames.disabled\r\n        ),\r\n        hidden: cn(\"invisible\", defaultClassNames.hidden),\r\n        ...classNames,\r\n      }}\r\n      components={{\r\n        Root: ({ className, rootRef, ...props }) => {\r\n          return (\r\n            <div\r\n              data-slot=\"calendar\"\r\n              ref={rootRef}\r\n              className={cn(className)}\r\n              {...props}\r\n            />\r\n          )\r\n        },\r\n        Chevron: ({ className, orientation, ...props }) => {\r\n          if (orientation === \"left\") {\r\n            return (\r\n              <ChevronLeftIcon className={cn(\"size-4\", className)} {...props} />\r\n            )\r\n          }\r\n\r\n          if (orientation === \"right\") {\r\n            return (\r\n              <ChevronRightIcon\r\n                className={cn(\"size-4\", className)}\r\n                {...props}\r\n              />\r\n            )\r\n          }\r\n\r\n          return (\r\n            <ChevronDownIcon className={cn(\"size-4\", className)} {...props} />\r\n          )\r\n        },\r\n        DayButton: CalendarDayButton,\r\n        WeekNumber: ({ children, ...props }) => {\r\n          return (\r\n            <td {...props}>\r\n              <div className=\"flex size-(--cell-size) items-center justify-center text-center\">\r\n                {children}\r\n              </div>\r\n            </td>\r\n          )\r\n        },\r\n        ...components,\r\n      }}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CalendarDayButton({\r\n  className,\r\n  day,\r\n  modifiers,\r\n  ...props\r\n}: React.ComponentProps<typeof DayButton>) {\r\n  const defaultClassNames = getDefaultClassNames()\r\n\r\n  const ref = React.useRef<HTMLButtonElement>(null)\r\n  React.useEffect(() => {\r\n    if (modifiers.focused) ref.current?.focus()\r\n  }, [modifiers.focused])\r\n\r\n  return (\r\n    <Button\r\n      ref={ref}\r\n      variant=\"ghost\"\r\n      size=\"icon\"\r\n      data-day={day.date.toLocaleDateString()}\r\n      data-selected-single={\r\n        modifiers.selected &&\r\n        !modifiers.range_start &&\r\n        !modifiers.range_end &&\r\n        !modifiers.range_middle\r\n      }\r\n      data-range-start={modifiers.range_start}\r\n      data-range-end={modifiers.range_end}\r\n      data-range-middle={modifiers.range_middle}\r\n      className={cn(\r\n        \"data-[selected-single=true]:bg-primary data-[selected-single=true]:text-primary-foreground data-[range-middle=true]:bg-accent data-[range-middle=true]:text-accent-foreground data-[range-start=true]:bg-primary data-[range-start=true]:text-primary-foreground data-[range-end=true]:bg-primary data-[range-end=true]:text-primary-foreground group-data-[focused=true]/day:border-ring group-data-[focused=true]/day:ring-ring/50 dark:hover:text-accent-foreground flex aspect-square size-auto w-full min-w-(--cell-size) flex-col gap-1 leading-none font-normal group-data-[focused=true]/day:relative group-data-[focused=true]/day:z-10 group-data-[focused=true]/day:ring-[3px] data-[range-end=true]:rounded-md data-[range-end=true]:rounded-r-md data-[range-middle=true]:rounded-none data-[range-start=true]:rounded-md data-[range-start=true]:rounded-l-md [&>span]:text-xs [&>span]:opacity-70\",\r\n        defaultClassNames.day,\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Calendar, CalendarDayButton }\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAKA;AAAA;AAEA;AACA;;;AAXA;;;;;;AAaA,SAAS,SAAS,EAChB,SAAS,EACT,UAAU,EACV,kBAAkB,IAAI,EACtB,gBAAgB,OAAO,EACvB,gBAAgB,OAAO,EACvB,UAAU,EACV,UAAU,EACV,GAAG,OAGJ;IACC,MAAM,oBAAoB,CAAA,GAAA,2LAAA,CAAA,uBAAoB,AAAD;IAE7C,qBACE,6LAAC,qKAAA,CAAA,YAAS;QACR,iBAAiB;QACjB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0JACA,OAAO,GAAG,CAAC,yCAAyC,CAAC,EACrD,OAAO,GAAG,CAAC,6CAA6C,CAAC,EACzD;QAEF,eAAe;QACf,YAAY;YACV,qBAAqB,CAAC,OACpB,KAAK,cAAc,CAAC,WAAW;oBAAE,OAAO;gBAAQ;YAClD,GAAG,UAAU;QACf;QACA,YAAY;YACV,MAAM,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,SAAS,kBAAkB,IAAI;YACxC,QAAQ,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACP,4CACA,kBAAkB,MAAM;YAE1B,OAAO,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B,kBAAkB,KAAK;YAC/D,KAAK,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACJ,2EACA,kBAAkB,GAAG;YAEvB,iBAAiB,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAc,IACxC,+DACA,kBAAkB,eAAe;YAEnC,aAAa,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACZ,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAc,IACxC,+DACA,kBAAkB,WAAW;YAE/B,eAAe,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,4EACA,kBAAkB,aAAa;YAEjC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uFACA,kBAAkB,SAAS;YAE7B,eAAe,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,uHACA,kBAAkB,aAAa;YAEjC,UAAU,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B,kBAAkB,QAAQ;YACrE,eAAe,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACd,2BACA,kBAAkB,UACd,YACA,2GACJ,kBAAkB,aAAa;YAEjC,OAAO;YACP,UAAU,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,kBAAkB,QAAQ;YAC/C,SAAS,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,iFACA,kBAAkB,OAAO;YAE3B,MAAM,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,oBAAoB,kBAAkB,IAAI;YACnD,oBAAoB,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACnB,+BACA,kBAAkB,kBAAkB;YAEtC,aAAa,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACZ,mDACA,kBAAkB,WAAW;YAE/B,KAAK,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACJ,6LACA,kBAAkB,GAAG;YAEvB,aAAa,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACZ,0BACA,kBAAkB,WAAW;YAE/B,cAAc,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,kBAAkB,YAAY;YAC/D,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0BAA0B,kBAAkB,SAAS;YACnE,OAAO,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACN,iFACA,kBAAkB,KAAK;YAEzB,SAAS,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACR,6DACA,kBAAkB,OAAO;YAE3B,UAAU,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACT,oCACA,kBAAkB,QAAQ;YAE5B,QAAQ,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,kBAAkB,MAAM;YAChD,GAAG,UAAU;QACf;QACA,YAAY;YACV,MAAM,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO;gBACrC,qBACE,6LAAC;oBACC,aAAU;oBACV,KAAK;oBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;oBACb,GAAG,KAAK;;;;;;YAGf;YACA,SAAS,CAAC,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,OAAO;gBAC5C,IAAI,gBAAgB,QAAQ;oBAC1B,qBACE,6LAAC,2NAAA,CAAA,kBAAe;wBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;wBAAa,GAAG,KAAK;;;;;;gBAElE;gBAEA,IAAI,gBAAgB,SAAS;oBAC3B,qBACE,6LAAC,6NAAA,CAAA,mBAAgB;wBACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;wBACvB,GAAG,KAAK;;;;;;gBAGf;gBAEA,qBACE,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;oBAAa,GAAG,KAAK;;;;;;YAElE;YACA,WAAW;YACX,YAAY,CAAC,EAAE,QAAQ,EAAE,GAAG,OAAO;gBACjC,qBACE,6LAAC;oBAAI,GAAG,KAAK;8BACX,cAAA,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;YAIT;YACA,GAAG,UAAU;QACf;QACC,GAAG,KAAK;;;;;;AAGf;KA5JS;AA8JT,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,EACH,SAAS,EACT,GAAG,OACoC;;IACvC,MAAM,oBAAoB,CAAA,GAAA,2LAAA,CAAA,uBAAoB,AAAD;IAE7C,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAqB;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;uCAAE;YACd,IAAI,UAAU,OAAO,EAAE,IAAI,OAAO,EAAE;QACtC;sCAAG;QAAC,UAAU,OAAO;KAAC;IAEtB,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,KAAK;QACL,SAAQ;QACR,MAAK;QACL,YAAU,IAAI,IAAI,CAAC,kBAAkB;QACrC,wBACE,UAAU,QAAQ,IAClB,CAAC,UAAU,WAAW,IACtB,CAAC,UAAU,SAAS,IACpB,CAAC,UAAU,YAAY;QAEzB,oBAAkB,UAAU,WAAW;QACvC,kBAAgB,UAAU,SAAS;QACnC,qBAAmB,UAAU,YAAY;QACzC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,o3BACA,kBAAkB,GAAG,EACrB;QAED,GAAG,KAAK;;;;;;AAGf;GApCS;MAAA", "debugId": null}}, {"offset": {"line": 4126, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Popover({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Root>) {\n  return <PopoverPrimitive.Root data-slot=\"popover\" {...props} />\n}\n\nfunction PopoverTrigger({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Trigger>) {\n  return <PopoverPrimitive.Trigger data-slot=\"popover-trigger\" {...props} />\n}\n\nfunction PopoverContent({\n  className,\n  align = \"center\",\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Content>) {\n  return (\n    <PopoverPrimitive.Portal>\n      <PopoverPrimitive.Content\n        data-slot=\"popover-content\"\n        align={align}\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden\",\n          className\n        )}\n        {...props}\n      />\n    </PopoverPrimitive.Portal>\n  )\n}\n\nfunction PopoverAnchor({\n  ...props\n}: React.ComponentProps<typeof PopoverPrimitive.Anchor>) {\n  return <PopoverPrimitive.Anchor data-slot=\"popover-anchor\" {...props} />\n}\n\nexport { Popover, PopoverTrigger, PopoverContent, PopoverAnchor }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBAAO,6LAAC,sKAAA,CAAA,OAAqB;QAAC,aAAU;QAAW,GAAG,KAAK;;;;;;AAC7D;KAJS;AAMT,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,6LAAC,sKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;MAJS;AAMT,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,QAAQ,EAChB,aAAa,CAAC,EACd,GAAG,OACmD;IACtD,qBACE,6LAAC,sKAAA,CAAA,SAAuB;kBACtB,cAAA,6LAAC,sKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,keACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;MApBS;AAsBT,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,6LAAC,sKAAA,CAAA,SAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;MAJS", "debugId": null}}, {"offset": {"line": 4207, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/medical/enhanced-appointment-booking.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { format } from \"date-fns\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Calendar } from \"@/components/ui/calendar\";\nimport { Card, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Separator } from \"@/components/ui/separator\";\nimport {\n  Popover,\n  PopoverContent,\n  PopoverTrigger,\n} from \"@/components/ui/popover\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\nimport { cn } from \"@/lib/utils\";\nimport { toast } from \"sonner\";\nimport { \n  Calendar as CalendarIcon, \n  Clock, \n  User, \n  FileText, \n  AlertTriangle,\n  Stethoscope,\n  Brain,\n  CheckCircle2\n} from \"lucide-react\";\nimport { EnhancedDoctor } from \"@/types/doctors\";\nimport { MedicalContext } from \"@/types/medical-documents\";\n\ninterface EnhancedAppointmentBookingProps {\n  patientId: string;\n  doctors: EnhancedDoctor[];\n  medicalContext?: MedicalContext;\n  preSelectedDoctorId?: string;\n  onAppointmentBooked?: (appointmentId: string) => void;\n}\n\nexport function EnhancedAppointmentBooking({\n  patientId,\n  doctors,\n  medicalContext,\n  preSelectedDoctorId,\n  onAppointmentBooked\n}: EnhancedAppointmentBookingProps) {\n  const [selectedDoctor, setSelectedDoctor] = useState<string>(preSelectedDoctorId || \"\");\n  const [date, setDate] = useState<Date>();\n  const [time, setTime] = useState<string>(\"\");\n  const [notes, setNotes] = useState<string>(\"\");\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  // Pre-populate notes with medical context\n  useEffect(() => {\n    if (medicalContext && !notes) {\n      const contextNotes = generateContextualNotes(medicalContext);\n      setNotes(contextNotes);\n    }\n  }, [medicalContext, notes]);\n\n  // Filter doctors based on medical context\n  const filteredDoctors = medicalContext \n    ? doctors.filter(doctor => \n        medicalContext.urgencyLevel === \"HIGH\" || \n        doctor.specialty || \n        doctor.name?.toLowerCase().includes(\"general\")\n      )\n    : doctors;\n\n  // Sort doctors by relevance\n  const sortedDoctors = filteredDoctors.sort((a, b) => {\n    // Prioritize pre-selected doctor\n    if (a.id === preSelectedDoctorId) return -1;\n    if (b.id === preSelectedDoctorId) return 1;\n    \n    // Prioritize by urgency and availability\n    if (medicalContext?.urgencyLevel === \"HIGH\") {\n      if (a.isAvailable && !b.isAvailable) return -1;\n      if (!a.isAvailable && b.isAvailable) return 1;\n    }\n    \n    // Sort by experience\n    const aExp = a.yearsOfExperience || 0;\n    const bExp = b.yearsOfExperience || 0;\n    return bExp - aExp;\n  });\n\n  const selectedDoctorData = doctors.find(d => d.id === selectedDoctor);\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n\n    if (!selectedDoctor || !date || !time) {\n      toast.error(\"Please fill in all required fields.\");\n      setIsSubmitting(false);\n      return;\n    }\n\n    const [hours, minutes] = time.split(\":\").map(Number);\n    const scheduledAt = new Date(date);\n    scheduledAt.setHours(hours);\n    scheduledAt.setMinutes(minutes);\n\n    try {\n      // Enhanced appointment creation with medical context\n      const appointmentData = {\n        patientId,\n        doctorId: selectedDoctor,\n        scheduledAt,\n        notes,\n        medicalContext: medicalContext ? JSON.stringify(medicalContext) : null,\n        urgencyLevel: medicalContext?.urgencyLevel || \"LOW\",\n        relatedDocumentIds: medicalContext?.documentIds || [],\n      };\n\n      // TODO: Replace with actual API call\n      // const result = await createEnhancedAppointment(appointmentData);\n      \n      toast.success(\"Appointment booked successfully!\");\n      \n      if (onAppointmentBooked) {\n        onAppointmentBooked(\"mock-appointment-id\");\n      }\n    } catch (error) {\n      toast.error(\"Failed to book appointment. Please try again.\");\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  const generateContextualNotes = (context: MedicalContext): string => {\n    const parts = [];\n    \n    if (context.symptoms.length > 0) {\n      parts.push(`Symptoms: ${context.symptoms.slice(0, 5).join(\", \")}`);\n    }\n    \n    if (context.diagnoses.length > 0) {\n      parts.push(`Conditions: ${context.diagnoses.slice(0, 3).join(\", \")}`);\n    }\n    \n    if (context.medications.length > 0) {\n      parts.push(`Current medications: ${context.medications.slice(0, 3).join(\", \")}`);\n    }\n    \n    if (context.keyFindings.length > 0) {\n      parts.push(`Key findings: ${context.keyFindings[0]}`);\n    }\n    \n    parts.push(`\\nBased on analysis of ${context.documentIds.length} medical document(s).`);\n    \n    return parts.join(\"\\n\\n\");\n  };\n\n  const getUrgencyColor = (urgency: string) => {\n    switch (urgency?.toLowerCase()) {\n      case \"high\":\n      case \"critical\":\n        return \"destructive\";\n      case \"medium\":\n        return \"default\";\n      case \"low\":\n      default:\n        return \"secondary\";\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Medical Context Summary */}\n      {medicalContext && (\n        <Card className=\"border-blue-200 dark:border-blue-800\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <Brain className=\"w-5 h-5 text-blue-600\" />\n              <span>Medical Context</span>\n              <Badge variant={getUrgencyColor(medicalContext.urgencyLevel)}>\n                {medicalContext.urgencyLevel} Priority\n              </Badge>\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"space-y-3\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\n              <div>\n                <span className=\"font-medium text-gray-700 dark:text-gray-300\">Key Symptoms:</span>\n                <p className=\"text-gray-600 dark:text-gray-400\">\n                  {medicalContext.symptoms.slice(0, 3).join(\", \")}\n                  {medicalContext.symptoms.length > 3 && ` (+${medicalContext.symptoms.length - 3} more)`}\n                </p>\n              </div>\n              <div>\n                <span className=\"font-medium text-gray-700 dark:text-gray-300\">Documents:</span>\n                <p className=\"text-gray-600 dark:text-gray-400\">\n                  {medicalContext.documentIds.length} medical document(s) analyzed\n                </p>\n              </div>\n            </div>\n            \n            {medicalContext.recommendedActions.length > 0 && (\n              <div>\n                <span className=\"font-medium text-gray-700 dark:text-gray-300\">Recommended Actions:</span>\n                <ul className=\"text-sm text-gray-600 dark:text-gray-400 mt-1 space-y-1\">\n                  {medicalContext.recommendedActions.slice(0, 2).map((action, index) => (\n                    <li key={index} className=\"flex items-start space-x-2\">\n                      <CheckCircle2 className=\"w-3 h-3 text-green-600 mt-0.5 flex-shrink-0\" />\n                      <span>{action}</span>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Appointment Booking Form */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <CalendarIcon className=\"w-5 h-5 text-green-600\" />\n            <span>Book Appointment</span>\n          </CardTitle>\n          {medicalContext && (\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n              Doctors are pre-filtered based on your medical analysis\n            </p>\n          )}\n        </CardHeader>\n        <CardContent>\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Doctor Selection */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"doctor\">Select Doctor *</Label>\n              <Select value={selectedDoctor} onValueChange={setSelectedDoctor}>\n                <SelectTrigger>\n                  <SelectValue placeholder=\"Choose a doctor\" />\n                </SelectTrigger>\n                <SelectContent>\n                  {sortedDoctors.map((doctor) => (\n                    <SelectItem key={doctor.id} value={doctor.id}>\n                      <div className=\"flex items-center justify-between w-full\">\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"font-medium\">Dr. {doctor.name}</span>\n                          {doctor.specialty && (\n                            <Badge variant=\"outline\" className=\"text-xs\">\n                              {doctor.specialty.replace('_', ' ')}\n                            </Badge>\n                          )}\n                        </div>\n                        <div className=\"flex items-center space-x-2 text-xs text-gray-500\">\n                          {doctor.yearsOfExperience && (\n                            <span>{doctor.yearsOfExperience}y exp</span>\n                          )}\n                          {doctor.consultationFee && (\n                            <span>${doctor.consultationFee}</span>\n                          )}\n                          {doctor.isAvailable && (\n                            <Badge variant=\"secondary\" className=\"text-xs\">Available</Badge>\n                          )}\n                        </div>\n                      </div>\n                    </SelectItem>\n                  ))}\n                </SelectContent>\n              </Select>\n              \n              {selectedDoctorData && (\n                <div className=\"p-3 bg-gray-50 dark:bg-gray-800 rounded-lg text-sm\">\n                  <div className=\"flex items-center space-x-2 mb-2\">\n                    <User className=\"w-4 h-4 text-gray-600\" />\n                    <span className=\"font-medium\">Dr. {selectedDoctorData.name}</span>\n                  </div>\n                  {selectedDoctorData.bio && (\n                    <p className=\"text-gray-600 dark:text-gray-400 text-xs\">\n                      {selectedDoctorData.bio}\n                    </p>\n                  )}\n                  <div className=\"flex items-center space-x-4 mt-2 text-xs text-gray-500\">\n                    {selectedDoctorData.yearsOfExperience && (\n                      <span>{selectedDoctorData.yearsOfExperience} years experience</span>\n                    )}\n                    {selectedDoctorData.consultationFee && (\n                      <span>Consultation: ${selectedDoctorData.consultationFee}</span>\n                    )}\n                  </div>\n                </div>\n              )}\n            </div>\n\n            <Separator />\n\n            {/* Date Selection */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"date\">Select Date *</Label>\n              <Popover>\n                <PopoverTrigger asChild>\n                  <Button\n                    variant=\"outline\"\n                    className={cn(\n                      \"w-full justify-start text-left font-normal\",\n                      !date && \"text-muted-foreground\"\n                    )}\n                  >\n                    <CalendarIcon className=\"mr-2 h-4 w-4\" />\n                    {date ? format(date, \"PPP\") : \"Pick a date\"}\n                  </Button>\n                </PopoverTrigger>\n                <PopoverContent className=\"w-auto p-0\">\n                  <Calendar\n                    mode=\"single\"\n                    selected={date}\n                    onSelect={setDate}\n                    initialFocus\n                    disabled={(date) => {\n                      const today = new Date();\n                      today.setHours(0, 0, 0, 0);\n\n                      const maxDate = new Date();\n                      maxDate.setDate(today.getDate() + 30);\n\n                      return (\n                        date < today ||\n                        date > maxDate ||\n                        date.getDay() === 0 // Disable Sundays\n                      );\n                    }}\n                  />\n                </PopoverContent>\n              </Popover>\n            </div>\n\n            {/* Time Selection */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"time\">Select Time *</Label>\n              <div className=\"flex items-center space-x-2\">\n                <Clock className=\"w-4 h-4 text-gray-500\" />\n                <Input\n                  id=\"time\"\n                  type=\"time\"\n                  value={time}\n                  onChange={(e) => setTime(e.target.value)}\n                  required\n                  min=\"09:00\"\n                  max=\"17:00\"\n                />\n              </div>\n              <p className=\"text-xs text-gray-500\">\n                Available hours: 9:00 AM - 5:00 PM\n              </p>\n            </div>\n\n            <Separator />\n\n            {/* Notes */}\n            <div className=\"space-y-2\">\n              <Label htmlFor=\"notes\">Additional Notes</Label>\n              <Textarea\n                id=\"notes\"\n                value={notes}\n                onChange={(e) => setNotes(e.target.value)}\n                placeholder=\"Any additional information or specific concerns...\"\n                rows={4}\n              />\n              {medicalContext && (\n                <p className=\"text-xs text-blue-600 dark:text-blue-400\">\n                  Notes have been pre-populated based on your medical analysis\n                </p>\n              )}\n            </div>\n\n            {/* Urgency Notice */}\n            {medicalContext?.urgencyLevel === \"HIGH\" && (\n              <div className=\"p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\">\n                <div className=\"flex items-center space-x-2\">\n                  <AlertTriangle className=\"w-4 h-4 text-red-600\" />\n                  <span className=\"text-sm font-medium text-red-800 dark:text-red-200\">\n                    High Priority Appointment\n                  </span>\n                </div>\n                <p className=\"text-xs text-red-700 dark:text-red-300 mt-1\">\n                  Based on your medical analysis, this appointment has been marked as high priority. \n                  Please consider booking the earliest available slot.\n                </p>\n              </div>\n            )}\n\n            {/* Submit Button */}\n            <Button \n              type=\"submit\" \n              className=\"w-full\" \n              disabled={isSubmitting}\n              size=\"lg\"\n            >\n              {isSubmitting ? (\n                <>\n                  <Clock className=\"w-4 h-4 mr-2 animate-spin\" />\n                  Booking Appointment...\n                </>\n              ) : (\n                <>\n                  <CalendarIcon className=\"w-4 h-4 mr-2\" />\n                  Book Appointment\n                </>\n              )}\n            </Button>\n          </form>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AAOA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AA1BA;;;;;;;;;;;;;;;;AA+CO,SAAS,2BAA2B,EACzC,SAAS,EACT,OAAO,EACP,cAAc,EACd,mBAAmB,EACnB,mBAAmB,EACa;;IAChC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,uBAAuB;IACpF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IAC/B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,0CAA0C;IAC1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gDAAE;YACR,IAAI,kBAAkB,CAAC,OAAO;gBAC5B,MAAM,eAAe,wBAAwB;gBAC7C,SAAS;YACX;QACF;+CAAG;QAAC;QAAgB;KAAM;IAE1B,0CAA0C;IAC1C,MAAM,kBAAkB,iBACpB,QAAQ,MAAM,CAAC,CAAA,SACb,eAAe,YAAY,KAAK,UAChC,OAAO,SAAS,IAChB,OAAO,IAAI,EAAE,cAAc,SAAS,cAEtC;IAEJ,4BAA4B;IAC5B,MAAM,gBAAgB,gBAAgB,IAAI,CAAC,CAAC,GAAG;QAC7C,iCAAiC;QACjC,IAAI,EAAE,EAAE,KAAK,qBAAqB,OAAO,CAAC;QAC1C,IAAI,EAAE,EAAE,KAAK,qBAAqB,OAAO;QAEzC,yCAAyC;QACzC,IAAI,gBAAgB,iBAAiB,QAAQ;YAC3C,IAAI,EAAE,WAAW,IAAI,CAAC,EAAE,WAAW,EAAE,OAAO,CAAC;YAC7C,IAAI,CAAC,EAAE,WAAW,IAAI,EAAE,WAAW,EAAE,OAAO;QAC9C;QAEA,qBAAqB;QACrB,MAAM,OAAO,EAAE,iBAAiB,IAAI;QACpC,MAAM,OAAO,EAAE,iBAAiB,IAAI;QACpC,OAAO,OAAO;IAChB;IAEA,MAAM,qBAAqB,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IAEtD,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,MAAM;YACrC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,gBAAgB;YAChB;QACF;QAEA,MAAM,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC;QAC7C,MAAM,cAAc,IAAI,KAAK;QAC7B,YAAY,QAAQ,CAAC;QACrB,YAAY,UAAU,CAAC;QAEvB,IAAI;YACF,qDAAqD;YACrD,MAAM,kBAAkB;gBACtB;gBACA,UAAU;gBACV;gBACA;gBACA,gBAAgB,iBAAiB,KAAK,SAAS,CAAC,kBAAkB;gBAClE,cAAc,gBAAgB,gBAAgB;gBAC9C,oBAAoB,gBAAgB,eAAe,EAAE;YACvD;YAEA,qCAAqC;YACrC,mEAAmE;YAEnE,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,IAAI,qBAAqB;gBACvB,oBAAoB;YACtB;QACF,EAAE,OAAO,OAAO;YACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,MAAM,QAAQ,EAAE;QAEhB,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC/B,MAAM,IAAI,CAAC,CAAC,UAAU,EAAE,QAAQ,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;QACnE;QAEA,IAAI,QAAQ,SAAS,CAAC,MAAM,GAAG,GAAG;YAChC,MAAM,IAAI,CAAC,CAAC,YAAY,EAAE,QAAQ,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;QACtE;QAEA,IAAI,QAAQ,WAAW,CAAC,MAAM,GAAG,GAAG;YAClC,MAAM,IAAI,CAAC,CAAC,qBAAqB,EAAE,QAAQ,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO;QACjF;QAEA,IAAI,QAAQ,WAAW,CAAC,MAAM,GAAG,GAAG;YAClC,MAAM,IAAI,CAAC,CAAC,cAAc,EAAE,QAAQ,WAAW,CAAC,EAAE,EAAE;QACtD;QAEA,MAAM,IAAI,CAAC,CAAC,uBAAuB,EAAE,QAAQ,WAAW,CAAC,MAAM,CAAC,qBAAqB,CAAC;QAEtF,OAAO,MAAM,IAAI,CAAC;IACpB;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAQ,SAAS;YACf,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;YACL;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,gCACC,6LAAC,mIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;8CACjB,6LAAC;8CAAK;;;;;;8CACN,6LAAC,oIAAA,CAAA,QAAK;oCAAC,SAAS,gBAAgB,eAAe,YAAY;;wCACxD,eAAe,YAAY;wCAAC;;;;;;;;;;;;;;;;;;kCAInC,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAK,WAAU;0DAA+C;;;;;;0DAC/D,6LAAC;gDAAE,WAAU;;oDACV,eAAe,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;oDACzC,eAAe,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,EAAE,eAAe,QAAQ,CAAC,MAAM,GAAG,EAAE,MAAM,CAAC;;;;;;;;;;;;;kDAG3F,6LAAC;;0DACC,6LAAC;gDAAK,WAAU;0DAA+C;;;;;;0DAC/D,6LAAC;gDAAE,WAAU;;oDACV,eAAe,WAAW,CAAC,MAAM;oDAAC;;;;;;;;;;;;;;;;;;;4BAKxC,eAAe,kBAAkB,CAAC,MAAM,GAAG,mBAC1C,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAA+C;;;;;;kDAC/D,6LAAC;wCAAG,WAAU;kDACX,eAAe,kBAAkB,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,sBAC1D,6LAAC;gDAAe,WAAU;;kEACxB,6LAAC,wNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;kEACxB,6LAAC;kEAAM;;;;;;;+CAFA;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAavB,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;;0CACT,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,6LAAC,6MAAA,CAAA,WAAY;wCAAC,WAAU;;;;;;kDACxB,6LAAC;kDAAK;;;;;;;;;;;;4BAEP,gCACC,6LAAC;gCAAE,WAAU;0CAA2C;;;;;;;;;;;;kCAK5D,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAK,UAAU;4BAAc,WAAU;;8CAEtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAS;;;;;;sDACxB,6LAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAgB,eAAe;;8DAC5C,6LAAC,qIAAA,CAAA,gBAAa;8DACZ,cAAA,6LAAC,qIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,6LAAC,qIAAA,CAAA,gBAAa;8DACX,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC,qIAAA,CAAA,aAAU;4DAAiB,OAAO,OAAO,EAAE;sEAC1C,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;;oFAAc;oFAAK,OAAO,IAAI;;;;;;;4EAC7C,OAAO,SAAS,kBACf,6LAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAU,WAAU;0FAChC,OAAO,SAAS,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;kFAIrC,6LAAC;wEAAI,WAAU;;4EACZ,OAAO,iBAAiB,kBACvB,6LAAC;;oFAAM,OAAO,iBAAiB;oFAAC;;;;;;;4EAEjC,OAAO,eAAe,kBACrB,6LAAC;;oFAAK;oFAAE,OAAO,eAAe;;;;;;;4EAE/B,OAAO,WAAW,kBACjB,6LAAC,oIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAY,WAAU;0FAAU;;;;;;;;;;;;;;;;;;2DAlBtC,OAAO,EAAE;;;;;;;;;;;;;;;;wCA2B/B,oCACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DAAK,WAAU;;gEAAc;gEAAK,mBAAmB,IAAI;;;;;;;;;;;;;gDAE3D,mBAAmB,GAAG,kBACrB,6LAAC;oDAAE,WAAU;8DACV,mBAAmB,GAAG;;;;;;8DAG3B,6LAAC;oDAAI,WAAU;;wDACZ,mBAAmB,iBAAiB,kBACnC,6LAAC;;gEAAM,mBAAmB,iBAAiB;gEAAC;;;;;;;wDAE7C,mBAAmB,eAAe,kBACjC,6LAAC;;gEAAK;gEAAgB,mBAAmB,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;8CAOlE,6LAAC,wIAAA,CAAA,YAAS;;;;;8CAGV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAO;;;;;;sDACtB,6LAAC,sIAAA,CAAA,UAAO;;8DACN,6LAAC,sIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8CACA,CAAC,QAAQ;;0EAGX,6LAAC,6MAAA,CAAA,WAAY;gEAAC,WAAU;;;;;;4DACvB,OAAO,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,SAAS;;;;;;;;;;;;8DAGlC,6LAAC,sIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACxB,cAAA,6LAAC,uIAAA,CAAA,WAAQ;wDACP,MAAK;wDACL,UAAU;wDACV,UAAU;wDACV,YAAY;wDACZ,UAAU,CAAC;4DACT,MAAM,QAAQ,IAAI;4DAClB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;4DAExB,MAAM,UAAU,IAAI;4DACpB,QAAQ,OAAO,CAAC,MAAM,OAAO,KAAK;4DAElC,OACE,OAAO,SACP,OAAO,WACP,KAAK,MAAM,OAAO,EAAE,kBAAkB;;wDAE1C;;;;;;;;;;;;;;;;;;;;;;;8CAOR,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAO;;;;;;sDACtB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC,oIAAA,CAAA,QAAK;oDACJ,IAAG;oDACH,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;oDACvC,QAAQ;oDACR,KAAI;oDACJ,KAAI;;;;;;;;;;;;sDAGR,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,6LAAC,wIAAA,CAAA,YAAS;;;;;8CAGV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAQ;;;;;;sDACvB,6LAAC,uIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,aAAY;4CACZ,MAAM;;;;;;wCAEP,gCACC,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;gCAO3D,gBAAgB,iBAAiB,wBAChC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;8DACzB,6LAAC;oDAAK,WAAU;8DAAqD;;;;;;;;;;;;sDAIvE,6LAAC;4CAAE,WAAU;sDAA8C;;;;;;;;;;;;8CAQ/D,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;oCACV,UAAU;oCACV,MAAK;8CAEJ,6BACC;;0DACE,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAA8B;;qEAIjD;;0DACE,6LAAC,6MAAA,CAAA,WAAY;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU3D;GApXgB;KAAA", "debugId": null}}, {"offset": {"line": 5054, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/app/%28app%29/patient-flow/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from \"@/components/ui/card\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Separator } from \"@/components/ui/separator\";\nimport { Progress } from \"@/components/ui/progress\";\nimport { \n  Upload, \n  Brain, \n  Stethoscope, \n  Calendar, \n  CheckCircle2,\n  ArrowRight,\n  FileText,\n  Clock,\n  AlertTriangle\n} from \"lucide-react\";\nimport { DocumentUpload } from \"@/components/medical/document-upload\";\nimport { AnalysisResults } from \"@/components/medical/analysis-results\";\nimport { SmartRouting } from \"@/components/medical/smart-routing\";\nimport { EnhancedAppointmentBooking } from \"@/components/medical/enhanced-appointment-booking\";\nimport { \n  UploadedFile, \n  DocumentUploadProgress, \n  AnalysisResult,\n  RoutingRecommendation,\n  MedicalContext\n} from \"@/types/medical-documents\";\nimport { EnhancedDoctor } from \"@/types/doctors\";\nimport { toast } from \"sonner\";\n\ntype FlowStep = \"upload\" | \"analysis\" | \"routing\" | \"booking\" | \"complete\";\n\ninterface PatientFlowState {\n  currentStep: FlowStep;\n  uploadedFiles: UploadedFile[];\n  uploadProgress: DocumentUploadProgress[];\n  analysisResults: AnalysisResult[];\n  routingRecommendations: RoutingRecommendation[];\n  medicalContext?: MedicalContext;\n  selectedDoctorId?: string;\n  appointmentId?: string;\n}\n\nexport default function PatientFlowPage() {\n  const [flowState, setFlowState] = useState<PatientFlowState>({\n    currentStep: \"upload\",\n    uploadedFiles: [],\n    uploadProgress: [],\n    analysisResults: [],\n    routingRecommendations: [],\n  });\n\n  const [isUploading, setIsUploading] = useState(false);\n  const [doctors, setDoctors] = useState<EnhancedDoctor[]>([]);\n\n  // Mock data - replace with actual API calls\n  useEffect(() => {\n    // Load doctors\n    setDoctors([\n      {\n        id: \"1\",\n        name: \"Sarah Johnson\",\n        email: \"<EMAIL>\",\n        specialty: \"CARDIOLOGY\" as any,\n        yearsOfExperience: 15,\n        consultationFee: 200,\n        isAvailable: true,\n      },\n      {\n        id: \"2\", \n        name: \"Michael Chen\",\n        email: \"<EMAIL>\",\n        specialty: \"NEUROLOGY\" as any,\n        yearsOfExperience: 12,\n        consultationFee: 250,\n        isAvailable: true,\n      },\n    ]);\n  }, []);\n\n  const handleFileUpload = async (files: File[], documentType: any) => {\n    setIsUploading(true);\n    \n    try {\n      // Convert files to UploadedFile format\n      const uploadedFiles: UploadedFile[] = files.map(file => ({\n        id: Math.random().toString(36).substring(7),\n        name: file.name,\n        size: file.size,\n        type: file.type,\n        preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined,\n        file,\n      }));\n\n      setFlowState(prev => ({\n        ...prev,\n        uploadedFiles: [...prev.uploadedFiles, ...uploadedFiles],\n      }));\n\n      // Simulate upload progress\n      const progressItems: DocumentUploadProgress[] = uploadedFiles.map(file => ({\n        fileId: file.id,\n        fileName: file.name,\n        progress: 0,\n        status: \"uploading\",\n      }));\n\n      setFlowState(prev => ({\n        ...prev,\n        uploadProgress: [...prev.uploadProgress, ...progressItems],\n      }));\n\n      // Simulate upload and analysis\n      for (const item of progressItems) {\n        await simulateProgress(item);\n      }\n\n      // Move to analysis step\n      setFlowState(prev => ({\n        ...prev,\n        currentStep: \"analysis\",\n      }));\n\n      toast.success(\"Documents uploaded and analyzed successfully!\");\n    } catch (error) {\n      toast.error(\"Failed to upload documents\");\n    } finally {\n      setIsUploading(false);\n    }\n  };\n\n  const simulateProgress = async (item: DocumentUploadProgress) => {\n    // Simulate upload progress\n    for (let i = 0; i <= 100; i += 10) {\n      await new Promise(resolve => setTimeout(resolve, 100));\n      setFlowState(prev => ({\n        ...prev,\n        uploadProgress: prev.uploadProgress.map(p => \n          p.fileId === item.fileId \n            ? { ...p, progress: i, status: i < 50 ? \"uploading\" : i < 100 ? \"analyzing\" : \"completed\" }\n            : p\n        ),\n      }));\n    }\n\n    // Add mock analysis result\n    const mockAnalysis: AnalysisResult = {\n      id: Math.random().toString(36).substring(7),\n      documentId: item.fileId,\n      status: \"COMPLETED\" as any,\n      symptoms: [\"headache\", \"nausea\", \"dizziness\"],\n      medications: [\"ibuprofen 400mg\", \"ondansetron 4mg\"],\n      diagnoses: [\"migraine\", \"tension headache\"],\n      suggestedTests: [\"MRI Brain\", \"Blood pressure check\"],\n      recommendedSpecialists: [\"Neurologist\", \"General Medicine\"],\n      urgencyLevel: \"MEDIUM\" as any,\n      confidence: 0.85,\n      processingTime: 2500,\n      suggestedDoctorIds: [\"1\", \"2\"],\n      routingReason: \"Neurological symptoms suggest specialist consultation\",\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    };\n\n    setFlowState(prev => ({\n      ...prev,\n      analysisResults: [...prev.analysisResults, mockAnalysis],\n    }));\n  };\n\n  const handleFileRemove = (fileId: string) => {\n    setFlowState(prev => ({\n      ...prev,\n      uploadedFiles: prev.uploadedFiles.filter(f => f.id !== fileId),\n      uploadProgress: prev.uploadProgress.filter(p => p.fileId !== fileId),\n      analysisResults: prev.analysisResults.filter(a => a.documentId !== fileId),\n    }));\n  };\n\n  const handleProceedToRouting = () => {\n    // Generate medical context from analysis results\n    const medicalContext: MedicalContext = {\n      documentIds: flowState.analysisResults.map(a => a.documentId),\n      symptoms: [...new Set(flowState.analysisResults.flatMap(a => a.symptoms))],\n      medications: [...new Set(flowState.analysisResults.flatMap(a => a.medications))],\n      diagnoses: [...new Set(flowState.analysisResults.flatMap(a => a.diagnoses))],\n      urgencyLevel: flowState.analysisResults.some(a => a.urgencyLevel === \"HIGH\") ? \"HIGH\" as any : \"MEDIUM\" as any,\n      analysisDate: new Date(),\n      keyFindings: [\"Neurological symptoms present\", \"Medication history documented\"],\n      recommendedActions: [\"Consult neurologist\", \"Monitor symptoms\", \"Follow up in 1 week\"],\n    };\n\n    // Generate routing recommendations\n    const recommendations: RoutingRecommendation[] = doctors\n      .filter(d => flowState.analysisResults.some(a => a.suggestedDoctorIds.includes(d.id)))\n      .map(doctor => ({\n        doctorId: doctor.id,\n        doctor,\n        matchScore: 0.8 + Math.random() * 0.2,\n        reason: `Specializes in ${doctor.specialty?.toLowerCase().replace('_', ' ')} relevant to your symptoms`,\n        urgencyLevel: medicalContext.urgencyLevel,\n        availableSlots: [new Date(Date.now() + ********), new Date(Date.now() + *********)],\n      }));\n\n    setFlowState(prev => ({\n      ...prev,\n      currentStep: \"routing\",\n      medicalContext,\n      routingRecommendations: recommendations,\n    }));\n  };\n\n  const handleBookAppointment = (doctorId: string, context: MedicalContext) => {\n    setFlowState(prev => ({\n      ...prev,\n      currentStep: \"booking\",\n      selectedDoctorId: doctorId,\n      medicalContext: context,\n    }));\n  };\n\n  const handleAppointmentBooked = (appointmentId: string) => {\n    setFlowState(prev => ({\n      ...prev,\n      currentStep: \"complete\",\n      appointmentId,\n    }));\n  };\n\n  const getStepProgress = () => {\n    const steps = [\"upload\", \"analysis\", \"routing\", \"booking\", \"complete\"];\n    const currentIndex = steps.indexOf(flowState.currentStep);\n    return ((currentIndex + 1) / steps.length) * 100;\n  };\n\n  const getStepStatus = (step: FlowStep) => {\n    const steps = [\"upload\", \"analysis\", \"routing\", \"booking\", \"complete\"];\n    const currentIndex = steps.indexOf(flowState.currentStep);\n    const stepIndex = steps.indexOf(step);\n    \n    if (stepIndex < currentIndex) return \"completed\";\n    if (stepIndex === currentIndex) return \"current\";\n    return \"pending\";\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 dark:from-background dark:via-background dark:to-background\">\n      <div className=\"container mx-auto px-4 py-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-4xl font-bold text-gray-900 dark:text-white mb-4\">\n            Smart Patient Flow\n          </h1>\n          <p className=\"text-xl text-gray-600 dark:text-gray-400 mb-6\">\n            Upload → Analyze → Route → Book - All in one seamless experience\n          </p>\n        </div>\n\n        {/* Progress Bar */}\n        <Card className=\"mb-8\">\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <span className=\"text-sm font-medium\">Overall Progress</span>\n              <span className=\"text-sm text-gray-500\">{Math.round(getStepProgress())}%</span>\n            </div>\n            <Progress value={getStepProgress()} className=\"h-2 mb-4\" />\n            \n            <div className=\"flex items-center justify-between\">\n              {[\n                { step: \"upload\" as FlowStep, icon: Upload, label: \"Upload\" },\n                { step: \"analysis\" as FlowStep, icon: Brain, label: \"Analysis\" },\n                { step: \"routing\" as FlowStep, icon: Stethoscope, label: \"Routing\" },\n                { step: \"booking\" as FlowStep, icon: Calendar, label: \"Booking\" },\n                { step: \"complete\" as FlowStep, icon: CheckCircle2, label: \"Complete\" },\n              ].map(({ step, icon: Icon, label }) => {\n                const status = getStepStatus(step);\n                return (\n                  <div key={step} className=\"flex flex-col items-center space-y-2\">\n                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${\n                      status === \"completed\" ? \"bg-green-600 text-white\" :\n                      status === \"current\" ? \"bg-blue-600 text-white\" :\n                      \"bg-gray-200 text-gray-500\"\n                    }`}>\n                      <Icon className=\"w-5 h-5\" />\n                    </div>\n                    <span className={`text-xs font-medium ${\n                      status === \"completed\" ? \"text-green-600\" :\n                      status === \"current\" ? \"text-blue-600\" :\n                      \"text-gray-500\"\n                    }`}>\n                      {label}\n                    </span>\n                  </div>\n                );\n              })}\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Step Content */}\n        <div className=\"max-w-4xl mx-auto\">\n          {flowState.currentStep === \"upload\" && (\n            <div className=\"space-y-6\">\n              <DocumentUpload\n                onFileUpload={handleFileUpload}\n                onFileRemove={handleFileRemove}\n                uploadProgress={flowState.uploadProgress}\n                uploadedFiles={flowState.uploadedFiles}\n                isUploading={isUploading}\n              />\n              \n              {flowState.uploadedFiles.length > 0 && !isUploading && (\n                <div className=\"text-center\">\n                  <Button \n                    onClick={() => setFlowState(prev => ({ ...prev, currentStep: \"analysis\" }))}\n                    size=\"lg\"\n                    className=\"px-8\"\n                  >\n                    Continue to Analysis\n                    <ArrowRight className=\"w-4 h-4 ml-2\" />\n                  </Button>\n                </div>\n              )}\n            </div>\n          )}\n\n          {flowState.currentStep === \"analysis\" && (\n            <div className=\"space-y-6\">\n              <div className=\"text-center mb-6\">\n                <h2 className=\"text-2xl font-bold mb-2\">Analysis Results</h2>\n                <p className=\"text-gray-600 dark:text-gray-400\">\n                  AI has analyzed your medical documents\n                </p>\n              </div>\n              \n              {flowState.analysisResults.map((result) => (\n                <AnalysisResults\n                  key={result.id}\n                  analysisResult={result}\n                  showActions={false}\n                />\n              ))}\n              \n              {flowState.analysisResults.length > 0 && (\n                <div className=\"text-center\">\n                  <Button \n                    onClick={handleProceedToRouting}\n                    size=\"lg\"\n                    className=\"px-8\"\n                  >\n                    Get Doctor Recommendations\n                    <ArrowRight className=\"w-4 h-4 ml-2\" />\n                  </Button>\n                </div>\n              )}\n            </div>\n          )}\n\n          {flowState.currentStep === \"routing\" && flowState.medicalContext && (\n            <div className=\"space-y-6\">\n              <div className=\"text-center mb-6\">\n                <h2 className=\"text-2xl font-bold mb-2\">Smart Recommendations</h2>\n                <p className=\"text-gray-600 dark:text-gray-400\">\n                  Based on your medical analysis, here are the best specialists for you\n                </p>\n              </div>\n              \n              <SmartRouting\n                recommendations={flowState.routingRecommendations}\n                medicalContext={flowState.medicalContext}\n                onBookAppointment={handleBookAppointment}\n              />\n            </div>\n          )}\n\n          {flowState.currentStep === \"booking\" && flowState.medicalContext && (\n            <div className=\"space-y-6\">\n              <div className=\"text-center mb-6\">\n                <h2 className=\"text-2xl font-bold mb-2\">Book Your Appointment</h2>\n                <p className=\"text-gray-600 dark:text-gray-400\">\n                  Complete your booking with pre-filled medical context\n                </p>\n              </div>\n              \n              <EnhancedAppointmentBooking\n                patientId=\"current-user-id\" // Replace with actual user ID\n                doctors={doctors}\n                medicalContext={flowState.medicalContext}\n                preSelectedDoctorId={flowState.selectedDoctorId}\n                onAppointmentBooked={handleAppointmentBooked}\n              />\n            </div>\n          )}\n\n          {flowState.currentStep === \"complete\" && (\n            <div className=\"text-center space-y-6\">\n              <div className=\"w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto\">\n                <CheckCircle2 className=\"w-10 h-10 text-green-600\" />\n              </div>\n              \n              <div>\n                <h2 className=\"text-3xl font-bold text-green-600 mb-2\">\n                  Appointment Booked Successfully!\n                </h2>\n                <p className=\"text-gray-600 dark:text-gray-400 mb-6\">\n                  Your appointment has been scheduled and the doctor has been provided with your medical context.\n                </p>\n                \n                <div className=\"space-y-4\">\n                  <Button size=\"lg\" className=\"px-8\">\n                    View Appointment Details\n                  </Button>\n                  <div>\n                    <Button variant=\"outline\" onClick={() => window.location.reload()}>\n                      Start New Flow\n                    </Button>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAGA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AASA;;;AA/BA;;;;;;;;;;;AA8Ce,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QAC3D,aAAa;QACb,eAAe,EAAE;QACjB,gBAAgB,EAAE;QAClB,iBAAiB,EAAE;QACnB,wBAAwB,EAAE;IAC5B;IAEA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAE3D,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,eAAe;YACf,WAAW;gBACT;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,WAAW;oBACX,mBAAmB;oBACnB,iBAAiB;oBACjB,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,WAAW;oBACX,mBAAmB;oBACnB,iBAAiB;oBACjB,aAAa;gBACf;aACD;QACH;oCAAG,EAAE;IAEL,MAAM,mBAAmB,OAAO,OAAe;QAC7C,eAAe;QAEf,IAAI;YACF,uCAAuC;YACvC,MAAM,gBAAgC,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACvD,IAAI,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC;oBACzC,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;oBACf,SAAS,KAAK,IAAI,CAAC,UAAU,CAAC,YAAY,IAAI,eAAe,CAAC,QAAQ;oBACtE;gBACF,CAAC;YAED,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,eAAe;2BAAI,KAAK,aAAa;2BAAK;qBAAc;gBAC1D,CAAC;YAED,2BAA2B;YAC3B,MAAM,gBAA0C,cAAc,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACzE,QAAQ,KAAK,EAAE;oBACf,UAAU,KAAK,IAAI;oBACnB,UAAU;oBACV,QAAQ;gBACV,CAAC;YAED,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,gBAAgB;2BAAI,KAAK,cAAc;2BAAK;qBAAc;gBAC5D,CAAC;YAED,+BAA+B;YAC/B,KAAK,MAAM,QAAQ,cAAe;gBAChC,MAAM,iBAAiB;YACzB;YAEA,wBAAwB;YACxB,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,aAAa;gBACf,CAAC;YAED,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,2BAA2B;QAC3B,IAAK,IAAI,IAAI,GAAG,KAAK,KAAK,KAAK,GAAI;YACjC,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACjD,aAAa,CAAA,OAAQ,CAAC;oBACpB,GAAG,IAAI;oBACP,gBAAgB,KAAK,cAAc,CAAC,GAAG,CAAC,CAAA,IACtC,EAAE,MAAM,KAAK,KAAK,MAAM,GACpB;4BAAE,GAAG,CAAC;4BAAE,UAAU;4BAAG,QAAQ,IAAI,KAAK,cAAc,IAAI,MAAM,cAAc;wBAAY,IACxF;gBAER,CAAC;QACH;QAEA,2BAA2B;QAC3B,MAAM,eAA+B;YACnC,IAAI,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC;YACzC,YAAY,KAAK,MAAM;YACvB,QAAQ;YACR,UAAU;gBAAC;gBAAY;gBAAU;aAAY;YAC7C,aAAa;gBAAC;gBAAmB;aAAkB;YACnD,WAAW;gBAAC;gBAAY;aAAmB;YAC3C,gBAAgB;gBAAC;gBAAa;aAAuB;YACrD,wBAAwB;gBAAC;gBAAe;aAAmB;YAC3D,cAAc;YACd,YAAY;YACZ,gBAAgB;YAChB,oBAAoB;gBAAC;gBAAK;aAAI;YAC9B,eAAe;YACf,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;QAEA,aAAa,CAAA,OAAQ,CAAC;gBACpB,GAAG,IAAI;gBACP,iBAAiB;uBAAI,KAAK,eAAe;oBAAE;iBAAa;YAC1D,CAAC;IACH;IAEA,MAAM,mBAAmB,CAAC;QACxB,aAAa,CAAA,OAAQ,CAAC;gBACpB,GAAG,IAAI;gBACP,eAAe,KAAK,aAAa,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBACvD,gBAAgB,KAAK,cAAc,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;gBAC7D,iBAAiB,KAAK,eAAe,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,KAAK;YACrE,CAAC;IACH;IAEA,MAAM,yBAAyB;QAC7B,iDAAiD;QACjD,MAAM,iBAAiC;YACrC,aAAa,UAAU,eAAe,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,UAAU;YAC5D,UAAU;mBAAI,IAAI,IAAI,UAAU,eAAe,CAAC,OAAO,CAAC,CAAA,IAAK,EAAE,QAAQ;aAAG;YAC1E,aAAa;mBAAI,IAAI,IAAI,UAAU,eAAe,CAAC,OAAO,CAAC,CAAA,IAAK,EAAE,WAAW;aAAG;YAChF,WAAW;mBAAI,IAAI,IAAI,UAAU,eAAe,CAAC,OAAO,CAAC,CAAA,IAAK,EAAE,SAAS;aAAG;YAC5E,cAAc,UAAU,eAAe,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,YAAY,KAAK,UAAU,SAAgB;YAC/F,cAAc,IAAI;YAClB,aAAa;gBAAC;gBAAiC;aAAgC;YAC/E,oBAAoB;gBAAC;gBAAuB;gBAAoB;aAAsB;QACxF;QAEA,mCAAmC;QACnC,MAAM,kBAA2C,QAC9C,MAAM,CAAC,CAAA,IAAK,UAAU,eAAe,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,kBAAkB,CAAC,QAAQ,CAAC,EAAE,EAAE,IAClF,GAAG,CAAC,CAAA,SAAU,CAAC;gBACd,UAAU,OAAO,EAAE;gBACnB;gBACA,YAAY,MAAM,KAAK,MAAM,KAAK;gBAClC,QAAQ,CAAC,eAAe,EAAE,OAAO,SAAS,EAAE,cAAc,QAAQ,KAAK,KAAK,0BAA0B,CAAC;gBACvG,cAAc,eAAe,YAAY;gBACzC,gBAAgB;oBAAC,IAAI,KAAK,KAAK,GAAG,KAAK;oBAAW,IAAI,KAAK,KAAK,GAAG,KAAK;iBAAW;YACrF,CAAC;QAEH,aAAa,CAAA,OAAQ,CAAC;gBACpB,GAAG,IAAI;gBACP,aAAa;gBACb;gBACA,wBAAwB;YAC1B,CAAC;IACH;IAEA,MAAM,wBAAwB,CAAC,UAAkB;QAC/C,aAAa,CAAA,OAAQ,CAAC;gBACpB,GAAG,IAAI;gBACP,aAAa;gBACb,kBAAkB;gBAClB,gBAAgB;YAClB,CAAC;IACH;IAEA,MAAM,0BAA0B,CAAC;QAC/B,aAAa,CAAA,OAAQ,CAAC;gBACpB,GAAG,IAAI;gBACP,aAAa;gBACb;YACF,CAAC;IACH;IAEA,MAAM,kBAAkB;QACtB,MAAM,QAAQ;YAAC;YAAU;YAAY;YAAW;YAAW;SAAW;QACtE,MAAM,eAAe,MAAM,OAAO,CAAC,UAAU,WAAW;QACxD,OAAO,AAAC,CAAC,eAAe,CAAC,IAAI,MAAM,MAAM,GAAI;IAC/C;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,QAAQ;YAAC;YAAU;YAAY;YAAW;YAAW;SAAW;QACtE,MAAM,eAAe,MAAM,OAAO,CAAC,UAAU,WAAW;QACxD,MAAM,YAAY,MAAM,OAAO,CAAC;QAEhC,IAAI,YAAY,cAAc,OAAO;QACrC,IAAI,cAAc,cAAc,OAAO;QACvC,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,6LAAC;4BAAE,WAAU;sCAAgD;;;;;;;;;;;;8BAM/D,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;kDACtC,6LAAC;wCAAK,WAAU;;4CAAyB,KAAK,KAAK,CAAC;4CAAmB;;;;;;;;;;;;;0CAEzE,6LAAC,uIAAA,CAAA,WAAQ;gCAAC,OAAO;gCAAmB,WAAU;;;;;;0CAE9C,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,MAAM;wCAAsB,MAAM,yMAAA,CAAA,SAAM;wCAAE,OAAO;oCAAS;oCAC5D;wCAAE,MAAM;wCAAwB,MAAM,uMAAA,CAAA,QAAK;wCAAE,OAAO;oCAAW;oCAC/D;wCAAE,MAAM;wCAAuB,MAAM,mNAAA,CAAA,cAAW;wCAAE,OAAO;oCAAU;oCACnE;wCAAE,MAAM;wCAAuB,MAAM,6MAAA,CAAA,WAAQ;wCAAE,OAAO;oCAAU;oCAChE;wCAAE,MAAM;wCAAwB,MAAM,wNAAA,CAAA,eAAY;wCAAE,OAAO;oCAAW;iCACvE,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE;oCAChC,MAAM,SAAS,cAAc;oCAC7B,qBACE,6LAAC;wCAAe,WAAU;;0DACxB,6LAAC;gDAAI,WAAW,CAAC,wDAAwD,EACvE,WAAW,cAAc,4BACzB,WAAW,YAAY,2BACvB,6BACA;0DACA,cAAA,6LAAC;oDAAK,WAAU;;;;;;;;;;;0DAElB,6LAAC;gDAAK,WAAW,CAAC,oBAAoB,EACpC,WAAW,cAAc,mBACzB,WAAW,YAAY,kBACvB,iBACA;0DACC;;;;;;;uCAbK;;;;;gCAiBd;;;;;;;;;;;;;;;;;8BAMN,6LAAC;oBAAI,WAAU;;wBACZ,UAAU,WAAW,KAAK,0BACzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,sJAAA,CAAA,iBAAc;oCACb,cAAc;oCACd,cAAc;oCACd,gBAAgB,UAAU,cAAc;oCACxC,eAAe,UAAU,aAAa;oCACtC,aAAa;;;;;;gCAGd,UAAU,aAAa,CAAC,MAAM,GAAG,KAAK,CAAC,6BACtC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,aAAa,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,aAAa;gDAAW,CAAC;wCACzE,MAAK;wCACL,WAAU;;4CACX;0DAEC,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;wBAO/B,UAAU,WAAW,KAAK,4BACzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,6LAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;gCAKjD,UAAU,eAAe,CAAC,GAAG,CAAC,CAAC,uBAC9B,6LAAC,uJAAA,CAAA,kBAAe;wCAEd,gBAAgB;wCAChB,aAAa;uCAFR,OAAO,EAAE;;;;;gCAMjB,UAAU,eAAe,CAAC,MAAM,GAAG,mBAClC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,MAAK;wCACL,WAAU;;4CACX;0DAEC,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;wBAO/B,UAAU,WAAW,KAAK,aAAa,UAAU,cAAc,kBAC9D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,6LAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;8CAKlD,6LAAC,oJAAA,CAAA,eAAY;oCACX,iBAAiB,UAAU,sBAAsB;oCACjD,gBAAgB,UAAU,cAAc;oCACxC,mBAAmB;;;;;;;;;;;;wBAKxB,UAAU,WAAW,KAAK,aAAa,UAAU,cAAc,kBAC9D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,6LAAC;4CAAE,WAAU;sDAAmC;;;;;;;;;;;;8CAKlD,6LAAC,sKAAA,CAAA,6BAA0B;oCACzB,WAAU,kBAAkB,8BAA8B;;oCAC1D,SAAS;oCACT,gBAAgB,UAAU,cAAc;oCACxC,qBAAqB,UAAU,gBAAgB;oCAC/C,qBAAqB;;;;;;;;;;;;wBAK1B,UAAU,WAAW,KAAK,4BACzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,wNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;8CAG1B,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDAGvD,6LAAC;4CAAE,WAAU;sDAAwC;;;;;;sDAIrD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,WAAU;8DAAO;;;;;;8DAGnC,6LAAC;8DACC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDAAC,SAAQ;wDAAU,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;kEAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYvF;GA9XwB;KAAA", "debugId": null}}]}