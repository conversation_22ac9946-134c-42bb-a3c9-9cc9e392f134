import { GoogleGenAI } from "@google/genai";

const ai = new GoogleGenAI({
  apiKey: process.env.NEXT_PUBLIC_GEMINI_API_KEY!,
});

export async function analyzeMedicalImage(base64: string, mimeType: string) {
  const result = await ai.models.generateContent({
    model: "gemini-1.5-flash",
    contents: [
      {
        inlineData: {
          data: base64,
          mimeType,
        },
      },
      {
        text: `
You are a medical document parser AI. Given the uploaded image or PDF of a medical report, prescription, or handwritten note, extract structured data in the following **strict JSON format**:

{
  "document_type": "Prescription | Lab Report | MRI Report | Discharge Summary | Other",
  "patient": {
    "name": "Full Name (if available)",
    "age": 45,
    "gender": "Male" // or "Female" or "Other"
  },
  "symptoms": ["fever", "headache", "nausea"],
  "medications": ["paracetamol 500mg", "ondansetron"],
  "diagnoses_or_keywords": ["Pneumonia", "Mild Dementia"],
  "suggested_tests": ["MRI Brain", "Blood CBC"],
  "recommended_specialists": ["Neurologist", "Pulmonologist"],
  "urgency": "Low | Medium | High"
}

Only return valid JSON without any additional explanation or text. If any data is missing or illegible, leave fields empty or null but maintain the structure.
        `,
      },
    ],
  });

  // Gemini returns a whole message, so extract JSON if wrapped in markdown/code blocks
  const raw = result.text?.trim() ?? "";
  const jsonMatch = raw.match(/\{[\s\S]*\}/);
  return jsonMatch?.[0] ?? "{}"; // Return JSON string for downstream parsing
}
