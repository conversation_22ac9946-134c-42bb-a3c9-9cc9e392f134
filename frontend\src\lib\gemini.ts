import { GoogleGenAI } from "@google/genai";
import { DocumentType, DoctorSpecialty, UrgencyLevel } from "@prisma/client";
import { GeminiAnalysisResponse } from "@/types/medical-documents";
import { getDocumentTypeConfig } from "@/lib/document-config";
import { SpecialtyConditionMapping } from "@/types/doctors";

const ai = new GoogleGenAI({
  apiKey: process.env.NEXT_PUBLIC_GEMINI_API_KEY!,
});

// Enhanced medical document analysis with document type-specific prompts
export async function analyzeMedicalDocument(
  base64: string,
  mimeType: string,
  documentType: DocumentType
): Promise<GeminiAnalysisResponse> {
  const config = getDocumentTypeConfig(documentType);
  const customPrompt = config?.analysisPrompt || getDefaultAnalysisPrompt();

  const result = await ai.models.generateContent({
    model: "gemini-1.5-flash",
    contents: [
      {
        inlineData: {
          data: base64,
          mimeType,
        },
      },
      {
        text: `
You are an advanced medical document analysis AI. Analyze the uploaded ${documentType
          .toLowerCase()
          .replace("_", " ")} document.

${customPrompt}

Extract structured data in the following **strict JSON format**:

{
  "document_type": "Prescription | Lab Report | MRI Report | X-Ray Report | Discharge Summary | Medical Certificate | Other",
  "patient": {
    "name": "Full Name (if available)",
    "age": 45,
    "gender": "Male" // or "Female" or "Other"
  },
  "symptoms": ["fever", "headache", "nausea"],
  "medications": ["paracetamol 500mg", "ondansetron"],
  "diagnoses_or_keywords": ["Pneumonia", "Mild Dementia"],
  "suggested_tests": ["MRI Brain", "Blood CBC"],
  "recommended_specialists": ["Neurologist", "Pulmonologist", "Cardiologist"],
  "urgency": "Low | Medium | High"
}

IMPORTANT GUIDELINES:
1. For urgency assessment:
   - "High": Critical conditions, emergency symptoms, abnormal vital signs
   - "Medium": Concerning findings that need prompt attention
   - "Low": Routine follow-ups, stable conditions
2. For recommended_specialists, use these exact terms:
   - "General Medicine", "Cardiology", "Neurology", "Orthopedics", "Dermatology"
   - "Pediatrics", "Gynecology", "Psychiatry", "Radiology", "Pathology"
   - "Oncology", "Endocrinology", "Gastroenterology", "Pulmonology"
   - "Nephrology", "Ophthalmology", "ENT", "Emergency Medicine"
3. Extract ALL medications with dosages if visible
4. Include ALL symptoms mentioned or implied
5. List ALL diagnoses, conditions, or medical keywords found

Only return valid JSON without any additional explanation or text. If any data is missing or illegible, leave fields empty or null but maintain the structure.
        `,
      },
    ],
  });

  // Extract and parse JSON response
  const raw = result.text?.trim() ?? "";
  const jsonMatch = raw.match(/\{[\s\S]*\}/);
  const jsonString = jsonMatch?.[0] ?? "{}";

  try {
    return JSON.parse(jsonString) as GeminiAnalysisResponse;
  } catch (error) {
    console.error("Failed to parse Gemini response:", error);
    throw new Error("Failed to parse AI analysis response");
  }
}

// Legacy function for backward compatibility
export async function analyzeMedicalImage(base64: string, mimeType: string) {
  const analysis = await analyzeMedicalDocument(
    base64,
    mimeType,
    DocumentType.OTHER
  );
  return JSON.stringify(analysis);
}

// Batch analysis for multiple documents
export async function batchAnalyzeMedicalDocuments(
  documents: Array<{
    base64: string;
    mimeType: string;
    documentType: DocumentType;
    fileName: string;
  }>
): Promise<
  Array<{ fileName: string; analysis: GeminiAnalysisResponse; error?: string }>
> {
  const results = await Promise.allSettled(
    documents.map(async (doc) => {
      const analysis = await analyzeMedicalDocument(
        doc.base64,
        doc.mimeType,
        doc.documentType
      );
      return { fileName: doc.fileName, analysis };
    })
  );

  return results.map((result, index) => {
    if (result.status === "fulfilled") {
      return result.value;
    } else {
      return {
        fileName: documents[index].fileName,
        analysis: getEmptyAnalysis(),
        error: result.reason?.message || "Analysis failed",
      };
    }
  });
}

// Smart routing based on analysis results
export function generateSmartRouting(analysis: GeminiAnalysisResponse): {
  recommendedSpecialties: DoctorSpecialty[];
  urgencyLevel: UrgencyLevel;
  routingReason: string;
} {
  const specialties = new Set<DoctorSpecialty>();
  const reasons: string[] = [];

  // Map urgency
  const urgencyLevel = mapUrgencyLevel(analysis.urgency);

  // Add specialties based on recommended specialists from AI
  analysis.recommended_specialists.forEach((specialist) => {
    const specialty = mapSpecialistToSpecialty(specialist);
    if (specialty) {
      specialties.add(specialty);
      reasons.push(`AI recommended ${specialist}`);
    }
  });

  // Add specialties based on symptoms and diagnoses
  const allConditions = [
    ...analysis.symptoms,
    ...analysis.diagnoses_or_keywords,
  ].map((item) => item.toLowerCase());

  allConditions.forEach((condition) => {
    Object.entries(SpecialtyConditionMapping).forEach(
      ([keyword, relatedSpecialties]) => {
        if (condition.includes(keyword.toLowerCase())) {
          relatedSpecialties.forEach((specialty) => {
            specialties.add(specialty);
            reasons.push(`Condition "${condition}" suggests ${specialty}`);
          });
        }
      }
    );
  });

  // Ensure at least General Medicine is included
  if (specialties.size === 0) {
    specialties.add(DoctorSpecialty.GENERAL_MEDICINE);
    reasons.push("Default routing to General Medicine");
  }

  return {
    recommendedSpecialties: Array.from(specialties),
    urgencyLevel,
    routingReason: reasons.join("; "),
  };
}

// Helper functions
function getDefaultAnalysisPrompt(): string {
  return `
Analyze this medical document and extract:
- Patient demographic information
- Medical conditions, symptoms, and diagnoses
- Medications and treatments
- Test results and recommendations
- Urgency level and follow-up needs
  `;
}

function getEmptyAnalysis(): GeminiAnalysisResponse {
  return {
    document_type: "Other",
    patient: {
      name: undefined,
      age: undefined,
      gender: undefined,
    },
    symptoms: [],
    medications: [],
    diagnoses_or_keywords: [],
    suggested_tests: [],
    recommended_specialists: [],
    urgency: "Low",
  };
}

function mapUrgencyLevel(urgency: string): UrgencyLevel {
  switch (urgency.toLowerCase()) {
    case "high":
    case "critical":
      return UrgencyLevel.HIGH;
    case "medium":
    case "moderate":
      return UrgencyLevel.MEDIUM;
    case "low":
    default:
      return UrgencyLevel.LOW;
  }
}

function mapSpecialistToSpecialty(specialist: string): DoctorSpecialty | null {
  const mapping: Record<string, DoctorSpecialty> = {
    "general medicine": DoctorSpecialty.GENERAL_MEDICINE,
    cardiology: DoctorSpecialty.CARDIOLOGY,
    cardiologist: DoctorSpecialty.CARDIOLOGY,
    neurology: DoctorSpecialty.NEUROLOGY,
    neurologist: DoctorSpecialty.NEUROLOGY,
    orthopedics: DoctorSpecialty.ORTHOPEDICS,
    orthopedist: DoctorSpecialty.ORTHOPEDICS,
    dermatology: DoctorSpecialty.DERMATOLOGY,
    dermatologist: DoctorSpecialty.DERMATOLOGY,
    pediatrics: DoctorSpecialty.PEDIATRICS,
    pediatrician: DoctorSpecialty.PEDIATRICS,
    gynecology: DoctorSpecialty.GYNECOLOGY,
    gynecologist: DoctorSpecialty.GYNECOLOGY,
    psychiatry: DoctorSpecialty.PSYCHIATRY,
    psychiatrist: DoctorSpecialty.PSYCHIATRY,
    radiology: DoctorSpecialty.RADIOLOGY,
    radiologist: DoctorSpecialty.RADIOLOGY,
    pathology: DoctorSpecialty.PATHOLOGY,
    pathologist: DoctorSpecialty.PATHOLOGY,
    oncology: DoctorSpecialty.ONCOLOGY,
    oncologist: DoctorSpecialty.ONCOLOGY,
    endocrinology: DoctorSpecialty.ENDOCRINOLOGY,
    endocrinologist: DoctorSpecialty.ENDOCRINOLOGY,
    gastroenterology: DoctorSpecialty.GASTROENTEROLOGY,
    gastroenterologist: DoctorSpecialty.GASTROENTEROLOGY,
    pulmonology: DoctorSpecialty.PULMONOLOGY,
    pulmonologist: DoctorSpecialty.PULMONOLOGY,
    nephrology: DoctorSpecialty.NEPHROLOGY,
    nephrologist: DoctorSpecialty.NEPHROLOGY,
    ophthalmology: DoctorSpecialty.OPHTHALMOLOGY,
    ophthalmologist: DoctorSpecialty.OPHTHALMOLOGY,
    ent: DoctorSpecialty.ENT,
    "emergency medicine": DoctorSpecialty.EMERGENCY_MEDICINE,
    emergency: DoctorSpecialty.EMERGENCY_MEDICINE,
    "family medicine": DoctorSpecialty.FAMILY_MEDICINE,
    anesthesiology: DoctorSpecialty.ANESTHESIOLOGY,
  };

  return mapping[specialist.toLowerCase()] || null;
}
