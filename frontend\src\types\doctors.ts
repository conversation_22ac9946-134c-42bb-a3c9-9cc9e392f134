import { Doctor<PERSON><PERSON><PERSON><PERSON> } from "@prisma/client";

export type Doctor = {
  id: string;
  name: string;
  email: string;
  specialty?: DoctorSpec<PERSON><PERSON>;
  licenseNumber?: string;
  yearsOfExperience?: number;
  bio?: string;
  consultationFee?: number;
};

// Extended doctor type with additional computed fields
export type EnhancedDoctor = Doctor & {
  rating?: number;
  availableSlots?: Date[];
  isAvailable?: boolean;
  patientCount?: number;
  nextAvailableSlot?: Date;
};

// Doctor specialty mapping for display
export const DoctorSpecialtyLabels: Record<DoctorSpecialty, string> = {
  GENERAL_MEDICINE: "General Medicine",
  CARDIOLOGY: "Cardiology",
  NEUROLOGY: "Neurology",
  ORTHOPEDICS: "Orthopedics",
  DERMATOLOGY: "Dermatology",
  PEDIATRICS: "Pediatrics",
  GYNECOLOGY: "Gynecology",
  PSYCHIATRY: "Psychiatry",
  RADIOLOGY: "Radiology",
  PATHOLOGY: "Pathology",
  ONCOLOGY: "Oncology",
  ENDOCRINOLOGY: "Endocrinology",
  GASTROENTEROLOGY: "Gastroenterology",
  PULMONOLOGY: "Pulmonology",
  NEPHROLOGY: "Nephrology",
  OPHTHALMOLOGY: "Ophthalmology",
  ENT: "ENT (Ear, Nose, Throat)",
  ANESTHESIOLOGY: "Anesthesiology",
  EMERGENCY_MEDICINE: "Emergency Medicine",
  FAMILY_MEDICINE: "Family Medicine",
};

// Specialty to condition mapping for smart routing
export const SpecialtyConditionMapping: Record<string, DoctorSpecialty[]> = {
  // Cardiovascular conditions
  heart: [DoctorSpecialty.CARDIOLOGY, DoctorSpecialty.GENERAL_MEDICINE],
  cardiac: [DoctorSpecialty.CARDIOLOGY],
  hypertension: [DoctorSpecialty.CARDIOLOGY, DoctorSpecialty.GENERAL_MEDICINE],
  "chest pain": [
    DoctorSpecialty.CARDIOLOGY,
    DoctorSpecialty.EMERGENCY_MEDICINE,
  ],

  // Neurological conditions
  headache: [DoctorSpecialty.NEUROLOGY, DoctorSpecialty.GENERAL_MEDICINE],
  migraine: [DoctorSpecialty.NEUROLOGY],
  seizure: [DoctorSpecialty.NEUROLOGY, DoctorSpecialty.EMERGENCY_MEDICINE],
  stroke: [DoctorSpecialty.NEUROLOGY, DoctorSpecialty.EMERGENCY_MEDICINE],
  dementia: [DoctorSpecialty.NEUROLOGY, DoctorSpecialty.PSYCHIATRY],

  // Orthopedic conditions
  fracture: [DoctorSpecialty.ORTHOPEDICS, DoctorSpecialty.EMERGENCY_MEDICINE],
  "joint pain": [DoctorSpecialty.ORTHOPEDICS, DoctorSpecialty.GENERAL_MEDICINE],
  arthritis: [DoctorSpecialty.ORTHOPEDICS],
  "back pain": [DoctorSpecialty.ORTHOPEDICS, DoctorSpecialty.GENERAL_MEDICINE],

  // Skin conditions
  rash: [DoctorSpecialty.DERMATOLOGY, DoctorSpecialty.GENERAL_MEDICINE],
  acne: [DoctorSpecialty.DERMATOLOGY],
  eczema: [DoctorSpecialty.DERMATOLOGY],
  psoriasis: [DoctorSpecialty.DERMATOLOGY],

  // Respiratory conditions
  cough: [DoctorSpecialty.PULMONOLOGY, DoctorSpecialty.GENERAL_MEDICINE],
  asthma: [DoctorSpecialty.PULMONOLOGY],
  pneumonia: [DoctorSpecialty.PULMONOLOGY, DoctorSpecialty.GENERAL_MEDICINE],
  "shortness of breath": [
    DoctorSpecialty.PULMONOLOGY,
    DoctorSpecialty.CARDIOLOGY,
  ],

  // Digestive conditions
  "stomach pain": [
    DoctorSpecialty.GASTROENTEROLOGY,
    DoctorSpecialty.GENERAL_MEDICINE,
  ],
  nausea: [DoctorSpecialty.GASTROENTEROLOGY, DoctorSpecialty.GENERAL_MEDICINE],
  diarrhea: [
    DoctorSpecialty.GASTROENTEROLOGY,
    DoctorSpecialty.GENERAL_MEDICINE,
  ],
  constipation: [
    DoctorSpecialty.GASTROENTEROLOGY,
    DoctorSpecialty.GENERAL_MEDICINE,
  ],

  // Mental health conditions
  depression: [DoctorSpecialty.PSYCHIATRY, DoctorSpecialty.GENERAL_MEDICINE],
  anxiety: [DoctorSpecialty.PSYCHIATRY, DoctorSpecialty.GENERAL_MEDICINE],
  insomnia: [DoctorSpecialty.PSYCHIATRY, DoctorSpecialty.GENERAL_MEDICINE],

  // Endocrine conditions
  diabetes: [DoctorSpecialty.ENDOCRINOLOGY, DoctorSpecialty.GENERAL_MEDICINE],
  thyroid: [DoctorSpecialty.ENDOCRINOLOGY],
  hormone: [DoctorSpecialty.ENDOCRINOLOGY, DoctorSpecialty.GYNECOLOGY],

  // Eye conditions
  vision: [DoctorSpecialty.OPHTHALMOLOGY],
  "eye pain": [DoctorSpecialty.OPHTHALMOLOGY],
  "blurred vision": [DoctorSpecialty.OPHTHALMOLOGY, DoctorSpecialty.NEUROLOGY],

  // General conditions
  fever: [DoctorSpecialty.GENERAL_MEDICINE, DoctorSpecialty.EMERGENCY_MEDICINE],
  fatigue: [DoctorSpecialty.GENERAL_MEDICINE],
  "weight loss": [DoctorSpecialty.GENERAL_MEDICINE, DoctorSpecialty.ONCOLOGY],
  "weight gain": [
    DoctorSpecialty.GENERAL_MEDICINE,
    DoctorSpecialty.ENDOCRINOLOGY,
  ],
};
