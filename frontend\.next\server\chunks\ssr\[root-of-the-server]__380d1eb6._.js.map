{"version": 3, "sources": [], "sections": [{"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from \"@prisma/client\";\r\n\r\nconst globalForPrisma = global as unknown as {\r\n  prisma: PrismaClient;\r\n};\r\n\r\nconst prisma = globalForPrisma.prisma || new PrismaClient();\r\n\r\nif (process.env.NODE_ENV !== \"production\") globalForPrisma.prisma = prisma;\r\n\r\nexport default prisma;\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIxB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEzD,wCAA2C,gBAAgB,MAAM,GAAG;uCAErD", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/lib/auth.ts"], "sourcesContent": ["import { betterAuth, BetterAuthOptions } from \"better-auth\";\r\nimport { prismaAdapter } from \"better-auth/adapters/prisma\";\r\nimport prisma from \"./prisma\";\r\n\r\nexport const auth = betterAuth({\r\n  database: prismaAdapter(prisma, {\r\n    provider: \"mongodb\",\r\n  }),\r\n  socialProviders: {\r\n    google: {\r\n      clientId: process.env.GOOGLE_CLIENT_ID as string,\r\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,\r\n    },\r\n  },\r\n  session: {\r\n    expiresIn: 60 * 60 * 24 * 7, // 7 days\r\n    updateAge: 60 * 60 * 24, // 1 day (every 1 day the session expiration is updated)\r\n    cookieCache: {\r\n      enabled: true,\r\n      maxAge: 5 * 60, // Cache duration in seconds\r\n    },\r\n  },\r\n  user: {\r\n    additionalFields: {\r\n      role: {\r\n        type: \"string\",\r\n        required: false,\r\n      },\r\n    },\r\n  },\r\n} satisfies BetterAuthOptions);\r\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;;;;AAEO,MAAM,OAAO,CAAA,GAAA,gKAAA,CAAA,aAAU,AAAD,EAAE;IAC7B,UAAU,CAAA,GAAA,iLAAA,CAAA,gBAAa,AAAD,EAAE,oHAAA,CAAA,UAAM,EAAE;QAC9B,UAAU;IACZ;IACA,iBAAiB;QACf,QAAQ;YACN,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;IACF;IACA,SAAS;QACP,WAAW,KAAK,KAAK,KAAK;QAC1B,WAAW,KAAK,KAAK;QACrB,aAAa;YACX,SAAS;YACT,QAAQ,IAAI;QACd;IACF;IACA,MAAM;QACJ,kBAAkB;YAChB,MAAM;gBACJ,MAAM;gBACN,UAAU;YACZ;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/lib/session.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport { auth } from \"./auth\";\r\nimport { headers } from \"next/headers\";\r\n\r\nexport async function getServerSession() {\r\n  const session = await auth.api.getSession({\r\n    headers: await headers(),\r\n  });\r\n\r\n  return session;\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;;;;AAEO,eAAe;IACpB,MAAM,UAAU,MAAM,kHAAA,CAAA,OAAI,CAAC,GAAG,CAAC,UAAU,CAAC;QACxC,SAAS,MAAM,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACvB;IAEA,OAAO;AACT;;;IANsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/actions/users.ts"], "sourcesContent": ["\"use server\";\r\n\r\nimport prisma from \"@/lib/prisma\";\r\nimport { Role } from \"@prisma/client\";\r\n\r\n// Function to update a user's role\r\nexport async function updateUserRole(userId: string, newRole: Role) {\r\n  try {\r\n    const updatedUser = await prisma.user.update({\r\n      where: { id: userId },\r\n      data: { role: newRole },\r\n    });\r\n    return updatedUser;\r\n  } catch (error) {\r\n    console.error(\"Error updating user role:\", error);\r\n    throw new Error(\"Failed to update user role\");\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;;;;;AAIO,eAAe,eAAe,MAAc,EAAE,OAAa;IAChE,IAAI;QACF,MAAM,cAAc,MAAM,oHAAA,CAAA,UAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC3C,OAAO;gBAAE,IAAI;YAAO;YACpB,MAAM;gBAAE,MAAM;YAAQ;QACxB;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM,IAAI,MAAM;IAClB;AACF;;;IAXsB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/.next-internal/server/app/%28auth%29/onboarding/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {getServerSession as '00b4ef6413cdcfd77c9f1aa606fece522653e36c4c'} from 'ACTIONS_MODULE0'\nexport {updateUserRole as '6057deb139db5e5ec029c14ad25f1cf6356a934ead'} from 'ACTIONS_MODULE1'\n"], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/auth/role-selection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/auth/role-selection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/role-selection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0S,GACvU,wEACA", "debugId": null}}, {"offset": {"line": 265, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/components/auth/role-selection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/auth/role-selection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/auth/role-selection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsR,GACnT,oDACA", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/FrostByte/intellicure/frontend/src/app/%28auth%29/onboarding/page.tsx"], "sourcesContent": ["import { getServerSession } from \"@/lib/session\";\r\nimport RoleSelection from \"@/components/auth/role-selection\";\r\n\r\nexport default async function OnboardingPage() {\r\n  const user = await getServerSession();\r\n  if (!user) {\r\n    return (\r\n      <div className=\"flex flex-col items-center justify-center h-screen\">\r\n        <h1 className=\"text-2xl font-bold mb-4\">Please sign in to continue</h1>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex flex-col items-center justify-center max-h-screen w-full\">\r\n      {user.user?.name && (\r\n        <p className=\"text-lg mb-4\">Hello, {user.user.name}!</p>\r\n      )}\r\n      <RoleSelection userId={user.user?.id || \"\"} />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,eAAe;IAC5B,MAAM,OAAO,MAAM,CAAA,GAAA,qHAAA,CAAA,mBAAgB,AAAD;IAClC,IAAI,CAAC,MAAM;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAG,WAAU;0BAA0B;;;;;;;;;;;IAG9C;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,KAAK,IAAI,EAAE,sBACV,8OAAC;gBAAE,WAAU;;oBAAe;oBAAQ,KAAK,IAAI,CAAC,IAAI;oBAAC;;;;;;;0BAErD,8OAAC,+IAAA,CAAA,UAAa;gBAAC,QAAQ,KAAK,IAAI,EAAE,MAAM;;;;;;;;;;;;AAG9C", "debugId": null}}]}