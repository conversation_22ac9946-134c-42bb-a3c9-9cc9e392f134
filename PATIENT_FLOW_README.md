# Smart Patient Flow System

A comprehensive patient flow system that integrates prescription/medical report analysis with appointment booking using AI-powered document analysis and smart routing.

## 🌟 Features

### 1. **Document Upload Interface**
- **Multi-format Support**: Upload prescriptions, lab reports, MRI reports, X-rays, discharge summaries, and medical certificates
- **File Validation**: Automatic validation of file types (PDF, JPG, PNG, WebP) and size limits
- **Document Type Selection**: Smart categorization with type-specific analysis prompts
- **Progress Tracking**: Real-time upload and processing progress indicators
- **Preview & Management**: File preview, removal, and duplicate detection

### 2. **AI Analysis Integration**
- **Enhanced Gemini Integration**: Extended existing Gemini AI with medical document-specific prompts
- **Structured Data Extraction**: Extracts patient info, symptoms, medications, diagnoses, and recommendations
- **Document Type Awareness**: Tailored analysis based on document type (prescription vs lab report vs MRI)
- **Confidence Scoring**: AI confidence levels and processing time tracking
- **Batch Processing**: Support for analyzing multiple documents simultaneously

### 3. **Smart Routing System**
- **Specialty Mapping**: Intelligent mapping of symptoms/conditions to medical specialties
- **Doctor Recommendations**: AI-powered doctor suggestions based on analysis results
- **Urgency Assessment**: Automatic prioritization (Low/Medium/High) based on medical findings
- **Match Scoring**: Relevance scoring for doctor-patient matching
- **Availability Integration**: Real-time doctor availability and scheduling

### 4. **Enhanced Appointment Booking**
- **Medical Context Pre-population**: Forms auto-filled with analysis results
- **Priority Scheduling**: Urgent cases get priority booking slots
- **Contextual Notes**: Automatically generated appointment notes from medical analysis
- **Doctor Filtering**: Pre-filtered doctor lists based on medical needs
- **Integrated Experience**: Seamless flow from analysis to booking

## 🏗️ Architecture

### Database Schema Extensions
```prisma
// New enums
enum DocumentType {
  PRESCRIPTION, LAB_REPORT, MRI_REPORT, XRAY_REPORT, 
  DISCHARGE_SUMMARY, MEDICAL_CERTIFICATE, OTHER
}

enum DoctorSpecialty {
  GENERAL_MEDICINE, CARDIOLOGY, NEUROLOGY, ORTHOPEDICS,
  DERMATOLOGY, PEDIATRICS, GYNECOLOGY, PSYCHIATRY, // ... and more
}

enum UrgencyLevel { LOW, MEDIUM, HIGH, CRITICAL }
enum AnalysisStatus { PENDING, PROCESSING, COMPLETED, FAILED }

// New models
model MedicalDocument {
  id: String @id @default(cuid())
  fileName: String
  originalName: String
  fileSize: Int
  mimeType: String
  documentType: DocumentType
  filePath: String
  uploadedById: String
  checksum: String?
  isProcessed: Boolean @default(false)
  analysis: AnalysisResult?
  // ... timestamps and relations
}

model AnalysisResult {
  id: String @id @default(cuid())
  documentId: String @unique
  status: AnalysisStatus @default(PENDING)
  extractedData: String? // JSON
  symptoms: String[]
  medications: String[]
  diagnoses: String[]
  suggestedTests: String[]
  recommendedSpecialists: String[]
  urgencyLevel: UrgencyLevel @default(LOW)
  confidence: Float?
  suggestedDoctorIds: String[]
  routingReason: String?
  // ... timestamps and relations
}

// Enhanced User model for doctors
model User {
  // ... existing fields
  specialty: DoctorSpecialty?
  licenseNumber: String?
  yearsOfExperience: Int?
  bio: String?
  consultationFee: Float?
  uploadedDocuments: MedicalDocument[]
}

// Enhanced Appointment model
model Appointment {
  // ... existing fields
  medicalContext: String? // JSON
  urgencyLevel: UrgencyLevel @default(LOW)
  relatedDocumentIds: String[]
}
```

### Component Structure
```
src/components/medical/
├── document-upload.tsx          # Enhanced upload with type selection
├── analysis-results.tsx         # AI analysis display
├── smart-routing.tsx           # Doctor recommendations
└── enhanced-appointment-booking.tsx # Context-aware booking

src/types/
├── medical-documents.ts        # Document and analysis types
└── doctors.ts                 # Enhanced doctor types with specialties

src/lib/
├── document-config.ts         # Document type configurations
└── gemini.ts                 # Enhanced AI integration

src/actions/
└── medical-documents.ts       # Server actions for document management

src/app/(app)/
└── patient-flow/page.tsx     # Main integration page
```

## 🚀 Usage Flow

### 1. Document Upload
```typescript
// Users select document type and upload files
const handleFileUpload = async (files: File[], documentType: DocumentType) => {
  // Validate files
  // Upload to server
  // Trigger AI analysis
  // Update progress
}
```

### 2. AI Analysis
```typescript
// Enhanced Gemini analysis with document-specific prompts
const analysis = await analyzeMedicalDocument(base64, mimeType, documentType);
const routing = generateSmartRouting(analysis);
```

### 3. Smart Routing
```typescript
// Generate doctor recommendations based on analysis
const recommendations = await getSmartRoutingRecommendations(documentIds);
// Filter and rank doctors by relevance, availability, and urgency
```

### 4. Appointment Booking
```typescript
// Pre-populate booking form with medical context
const appointmentData = {
  patientId,
  doctorId: selectedDoctor,
  scheduledAt,
  medicalContext: JSON.stringify(medicalContext),
  urgencyLevel: medicalContext.urgencyLevel,
  relatedDocumentIds: medicalContext.documentIds,
}
```

## 🔧 Configuration

### Document Types
Each document type has specific configuration:
```typescript
{
  type: DocumentType.PRESCRIPTION,
  label: "Prescription",
  description: "Medical prescriptions and medication orders",
  acceptedMimeTypes: ["image/jpeg", "image/png", "application/pdf"],
  maxSize: 10 * 1024 * 1024, // 10MB
  analysisPrompt: "Analyze this prescription and extract medications, dosages, patient info..."
}
```

### Specialty Mapping
Conditions are mapped to medical specialties:
```typescript
const SpecialtyConditionMapping = {
  "heart": [DoctorSpecialty.CARDIOLOGY, DoctorSpecialty.GENERAL_MEDICINE],
  "headache": [DoctorSpecialty.NEUROLOGY, DoctorSpecialty.GENERAL_MEDICINE],
  // ... more mappings
}
```

## 🎯 Key Benefits

1. **Seamless Experience**: End-to-end flow from document upload to appointment booking
2. **AI-Powered Intelligence**: Smart analysis and routing based on medical content
3. **Time Saving**: Pre-populated forms and filtered doctor recommendations
4. **Better Outcomes**: Appropriate specialist matching and priority handling
5. **Comprehensive Context**: Doctors receive full medical context before appointments

## 🔮 Future Enhancements

1. **Real-time Notifications**: Push notifications for analysis completion and appointment confirmations
2. **Integration with EHR Systems**: Connect with existing Electronic Health Record systems
3. **Telemedicine Support**: Video consultation booking for remote appointments
4. **Multi-language Support**: Document analysis in multiple languages
5. **Advanced Analytics**: Patient flow analytics and optimization insights
6. **Mobile App**: Native mobile application for document capture and upload

## 🛠️ Technical Requirements

- **Frontend**: Next.js 14+, React 18+, TypeScript
- **Database**: MongoDB with Prisma ORM
- **AI Integration**: Google Gemini 1.5 Flash
- **File Storage**: Local filesystem (configurable for cloud storage)
- **Authentication**: Better Auth
- **UI Components**: Radix UI primitives with Tailwind CSS

## 📝 Next Steps

1. **Database Migration**: Run Prisma migrations to create new tables
2. **Environment Setup**: Configure Gemini API keys and file storage
3. **Testing**: Implement comprehensive test suite
4. **Deployment**: Deploy with proper file upload handling
5. **Documentation**: Create user guides and API documentation

This system transforms the traditional appointment booking process into an intelligent, context-aware experience that benefits both patients and healthcare providers.
