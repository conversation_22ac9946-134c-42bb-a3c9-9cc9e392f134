name: Sync <PERSON><PERSON>I App to HF Spaces
run-name: Sync <PERSON><PERSON>I App to HF Spaces

on:
  push:
    branches:
      - main
    paths:
      - "backend/**" # Only run if files in the backend/ directory changed

jobs:
  sync-app:
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.12"

      - name: Install dependencies
        run: |
          pip install huggingface_hub

      - name: Get the commit message
        id: get-commit-message
        run: echo "commit_message=$(git log -1 --pretty=%B)" >> $GITHUB_ENV

      - name: Check for changes in the backend directory
        id: check-changes
        run: |
          if git diff --quiet HEAD^ HEAD -- ./backend; then
            echo "changed=false" >> $GITHUB_OUTPUT
          else
            echo "changed=true" >> $GITHUB_OUTPUT
          fi

      - name: Sync FastAPI app to Hugging Face Space
        if: steps.check-changes.outputs.changed == 'true'
        env:
          HF_TOKEN: ${{ secrets.HF_TOKEN }}
          COMMIT_MESSAGE: ${{ env.commit_message }}
          HF_SPACE_ID: ${{ secrets.HF_SPACE_ID }}
        run: |
          python - <<EOF
          from huggingface_hub import HfApi
          import os
          import shutil

          api = HfApi(token=os.getenv("HF_TOKEN"))

          backend_folder = "./backend"
          if not os.path.exists(backend_folder):
              print(f"Error: The folder '{backend_folder}' does not exist.")
              exit(1)

          temp_upload_dir = "/tmp/backend_upload"
          shutil.copytree(backend_folder, temp_upload_dir, dirs_exist_ok=True)

          api.upload_folder(
              folder_path=temp_upload_dir,
              repo_id=os.getenv("HF_SPACE_ID"),
              repo_type="space",
              commit_message=os.getenv("COMMIT_MESSAGE")
          )

          shutil.rmtree(temp_upload_dir)
          EOF
